# Agent

## installation
[installation](https://github.com/BrainBlend-AI/atomic-agents#Installation)

### run ollama qwen3:8b model
prerequisite
```
ollama pull qwen3:8b
ollama run qwen3:8b
```

## test index agent
run index server and client to test the shortage index agent

server.py
```
$ cd /qct/develop/agent/index/server.py
$ python3 server.py
```

client.py
```
$ cd /qct/develop/agent/index/client.py
$ python3 client.py
```

[question sample](index/question.md)

## run notification
run notification server and client to test the shortage notification agent

mqtt web protocol: 10.101.8.103:18083

http server
```
$ cd /qct/develop/agent/http
$ python3 server.py
```

server.py
```
$ cd /qct/develop/agent/notification/server.py
$ python3 server.py
```

client.py
```
$ cd /qct/develop/agent/notification/client.py
$ python3 client.py
```

[question sample](notification/question.md)