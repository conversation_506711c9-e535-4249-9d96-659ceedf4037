from qdrant_client import QdrantClient
from qdrant_client.http.models import VectorParams, Distance
import os
os.environ["HF_HOME"] = "/home/<USER>/jim/db_agent_develop/.hf_cache"
from sentence_transformers import SentenceTransformer

class QdrantService:
    """Connect to Qdrant database."""
    def __init__(self):
        self.qdrant_url = os.getenv("QDRANT_URL", "http://10.101.8.110:6333")
        self.collection_name = os.getenv("COLLECTION_NAME", "trial_01")
        self.emb_model_name = os.getenv("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")

        self.client = QdrantClient(url=self.qdrant_url)
        self.embedder = SentenceTransformer(self.emb_model_name)

        self._init_collection()

    def _init_collection(self):
        """Create collection if it does not exist."""
        if not self.client.collection_exists(self.collection_name):
            dim = self.embedder.get_sentence_embedding_dimension()
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config={"text": VectorParams(size=dim, distance=Distance.COSINE)},
            )
