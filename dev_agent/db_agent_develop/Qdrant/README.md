## 目錄架構
```plaintext
Qdrant/
├── server.py                       # Implementation for SSE transport
├── client.py                       # User chat with agent here
├── interfaces/                     # Base classes/interfaces for tools and resources
│   └── tool.py
├── services/
│   └── qdrant_connection.py        # Connect to local Qdrant
├── tools/
│   ├── find.py                     # find
│   ├── store.py                    # store
│   └── forget.py                   # forget
├── resources/
├── pictures/
│   ├── demo_qdrant_crud.png        # demo_CRUD_scenario
│   └── demo_qdrant_collection.png  # the Qdrant Dashboard
└── README.md

```

## Qdrant Dashboard
Create the MongoDB service on L4*2 server.(************)  

The Qdrant Dashboard is available at 
http://************:6333/dashboard#/collections/trial_01  

(I use the collection named "trial_01")  

![demo_qdrant_collection](/Qdrant/pictures/demo_qdrant_collection.png)

## Quick Setup

### Use uv to setup environment
After install uv
```bash
uv venv .venv
source .venv/bin/activate
uv pip sync
```

### Start  Server

```bash
uv run Qdrant/server.py
```

### Start Client

```bash
uv run Qdrant/client.py
```

### 開始問問題

- 啟動 `client.py` 後，即可直接在 terminal 輸入自然語言問問題.  
---

## Results  
### Interact with Qdrant  
``` code
What model of headphones does Jim use?
```

``` code
Please remember that the headphones Jim is using are AirPods Pro 2.
```

``` code
What model of headphones does Jim use?
```

### Entire Conversation:  

![demo_Qdrant_CRUD](/Qdrant/pictures/demo_qdrant_crud.png)  

