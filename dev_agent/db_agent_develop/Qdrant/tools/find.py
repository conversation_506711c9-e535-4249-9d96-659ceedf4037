from services.qdrant_connection import QdrantService
from interfaces.tool import Too<PERSON>, ToolResponse, BaseToolInput
from pydantic import BaseModel, Field
from typing import List

qdrant = QdrantService()

class QdrantFind_Input_Tool(BaseToolInput):
    query: str = Field(..., description="Query string to search related memory")
    top_k: int = Field(10, description="Number of top matches to return")

class QdrantFind_Output_Tool(BaseModel):
    results: List[str]

class QdrantFindTool(Tool):
    name = "qdrant_find"
    description = "Search for related memories in Qdrant"
    input_model = QdrantFind_Input_Tool
    output_model = QdrantFind_Output_Tool

    async def execute(self, input_data: QdrantFind_Input_Tool) -> ToolResponse:
        vector = qdrant.embedder.encode(input_data.query).tolist()
        hits = qdrant.client.search(
            collection_name=qdrant.collection_name,
            query_vector=("text", vector),
            limit=input_data.top_k,
        )
        results = [hit.payload["text"] for hit in hits]

        return ToolResponse.from_model(QdrantFind_Output_Tool(results=results))
