from services.qdrant_connection import QdrantService
from interfaces.tool import Too<PERSON>, ToolResponse, BaseToolInput
from qdrant_client.http import models
from pydantic import BaseModel, Field

qdrant = QdrantService()

class QdrantForget_Input_Tool(BaseToolInput):
    query: str = Field(..., description="Query string to find and forget relevant memory")
    top_k: int = Field(10, description="Number of top matches to consider for deletion")

class QdrantForget_Output_Tool(BaseModel):
    message: str

class QdrantForgetTool(Tool):
    name = "qdrant_forget"
    description = "Find the most relevant memory and delete it from Qdrant"
    input_model = QdrantForget_Input_Tool
    output_model = QdrantForget_Output_Tool

    async def execute(self, input_data: QdrantForget_Input_Tool) -> ToolResponse:
        vector = qdrant.embedder.encode(input_data.query).tolist()
        hits = qdrant.client.search(
            collection_name=qdrant.collection_name,
            query_vector=("text", vector),
            limit=input_data.top_k,
        )

        if not hits:
            return ToolResponse.from_model(QdrantForget_Output_Tool(message="Cannot find any related memory to forget."))

        hit = hits[0]
        qdrant.client.delete(
            collection_name=qdrant.collection_name,
            points_selector=models.PointIdsList(points=[hit.id]),
            wait=True,
        )
        return ToolResponse.from_model(QdrantForget_Output_Tool(message=f"Forgot：「{hit.payload['text']}」"))
