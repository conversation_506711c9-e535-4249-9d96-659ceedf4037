from services.qdrant_connection import QdrantService
from interfaces.tool import Too<PERSON>, ToolResponse, BaseToolInput
from qdrant_client.http.models import PointStruct
from pydantic import BaseModel, Field
import uuid

qdrant = QdrantService()

class QdrantStore_Input_Tool(BaseToolInput):
    text: str = Field(..., description="Text content to store in Qdrant")

class QdrantStore_Output_Tool(BaseModel):
    point_id: str

class QdrantStoreTool(Tool):
    name = "qdrant_store"
    description = "Store a memory into Qdrant"
    input_model = QdrantStore_Input_Tool
    output_model = QdrantStore_Output_Tool

    async def execute(self, input_data: QdrantStore_Input_Tool) -> ToolResponse:
        vector = qdrant.embedder.encode(input_data.text).tolist()
        pid = str(uuid.uuid4())
        qdrant.client.upsert(
            collection_name=qdrant.collection_name,
            wait=True,
            points=[
                PointStruct(id=pid, vector={"text": vector}, payload={"text": input_data.text})
            ]
        )
        return ToolResponse.from_model(QdrantStore_Output_Tool(point_id=pid))
