import sys
import u<PERSON>orn
from typing import List

from starlette.applications import Starlette
from starlette.requests import Request
from starlette.routing import Mount, Route
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

from mcp.server.fastmcp import FastMCP
from mcp.server import Server
from mcp.server.sse import SseServerTransport

from interfaces.tool import Tool
from tools.store import QdrantStoreTool
from tools.find import QdrantF<PERSON>Tool
from tools.forget import QdrantF<PERSON>getTool

def get_available_tools() -> List[Tool]:
    return [
        QdrantStoreTool(),
        QdrantFindTool(),
        QdrantForgetTool(),
    ]

def create_starlette_app(mcp_server: Server) -> Starlette:
    # Create SSE transport
    sse = SseServerTransport("/messages/")

    # Define GET /sse 的 handler
    async def handle_sse(request: Request):
        async with sse.connect_sse(request.scope, request.receive, request._send) as (r, w):
            await mcp_server.run(r, w, mcp_server.create_initialization_options())

    # CORS middleware
    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
            allow_credentials=True,
        )
    ]

    # Starlette Route：GET /sse, POST /messages
    return Starlette(
        routes=[
            Route("/sse", endpoint=handle_sse),
            Mount("/messages/", app=sse.handle_post_message),
        ],
        middleware=middleware,
    )


# 用 FastMCP 建立 MCP server
mcp = FastMCP("qdrant-mcp-server", log_level="INFO")
for tool in get_available_tools():
    mcp.add_tool(
        fn=tool.execute,
        name=tool.name,
        description=tool.__doc__,            # 使用 docstring 作為工具描述
        structured_output=tool.output_model, # Output Schema
    )

# 宣告 Starlette app
app = create_starlette_app(mcp._mcp_server)

if __name__ == "__main__":
    print("🚀 Qdrant SSE MCP Server ready at http://127.0.0.1:8706/sse", file=sys.stderr)
    uvicorn.run(
        "server:app",
        host="127.0.0.1",
        port=8706,
        reload=False,
        log_level="info",
    )
