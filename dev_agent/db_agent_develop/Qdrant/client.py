# pyright: reportInvalidTypeForm=false
from atomic_agents.lib.factories.mcp_tool_factory import fetch_mcp_tools
from rich.console import Console
from rich.table import Table
import openai
import os
import instructor
from pydantic import Field
from atomic_agents.agents.base_agent import BaseIOSchema, BaseAgent, BaseAgentConfig
from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator
from atomic_agents.lib.components.agent_memory import AgentMemory
from typing import Union, Type, Dict, Optional
from dataclasses import dataclass

model_name = "meta-llama/Llama-3.3-70B-Instruct"
vLLM_url = "http://************:8701/v1"

# 1. Configuration and environment setup
@dataclass
class MCPConfig:
    """Configuration for the MCP Agent system using SSE transport."""

    mcp_server_url: str = "http://localhost:8706"

    # NOTE: In contrast to other examples, we use gpt-4o and not gpt-4o-mini here.
    # In my tests, gpt-4o-mini was not smart enough to deal with multiple tools like that
    # and at the moment MCP does not yet allow for adding sufficient metadata to
    # clarify tools even more and introduce more constraints.
    openai_model: str = "meta-llama/Llama-3.3-70B-Instruct"
    openai_api_key: str = os.getenv("OPENAI_API_KEY")
    """
    def __post_init__(self):
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
    """

config = MCPConfig()
console = Console()
client = instructor.from_openai(openai.OpenAI(base_url=vLLM_url, api_key="OPENAI_API_KEY"), mode=instructor.Mode.JSON)


class FinalResponseSchema(BaseIOSchema):
    """Schema for providing a final text response to the user."""

    response_text: str = Field(..., description="The final text response to the user's query")


# Fetch tools and build ActionUnion statically
tools = fetch_mcp_tools(
    mcp_endpoint=config.mcp_server_url,
    use_stdio=False,
)
if not tools:
    raise RuntimeError("No MCP tools found. Please ensure the MCP server is running and accessible.")

# Build mapping from input_schema to ToolClass
tool_schema_to_class_map: Dict[Type[BaseIOSchema], Type[BaseAgent]] = {
    ToolClass.input_schema: ToolClass for ToolClass in tools if hasattr(ToolClass, "input_schema")
}
# Collect all tool input schemas
tool_input_schemas = tuple(tool_schema_to_class_map.keys())
print("[Debug]")
print("Tool input schemas:", [schema.__name__ for schema in tool_input_schemas])
# Available schemas include all tool input schemas and the final response schema
available_schemas = tool_input_schemas + (FinalResponseSchema,)
print("[Debug]")
print("Available schemas:", [schema.__name__ for schema in available_schemas])

# Define the Union of all action schemas
ActionUnion = Union[*available_schemas]

def safe_orchestrator_run(agent, *args, **kwargs):
    max_retry = 30
    example_schema = {
        "reasoning": "To find Jim's phone model, I will use qdrant_find.",
        "action": {
            "tool_name": "qdrant_find",
            "input_data": {
                "query": "Jim's phone model",
                "top_k": 3
            }
        }
    }

    for i in range(max_retry):
        try:
            output = agent.run(*args, **kwargs)
            if hasattr(output, "action") and output.action is not None:
                return output
            else:
                agent.memory.add_message(
                    "system",
                    {"query": (
                        "Missing 'action' field. Follow this format:\n"
                        f"{example_schema}"
                    )}
                )
        except Exception as e:
            agent.memory.add_message(
                "system",
                {"query": (
                    f"Validation error:\n{str(e)}\nExpected format:\n{example_schema}"
                )}
            )
    raise RuntimeError("LLM failed to produce valid output after multiple retries.")

# 2. Schema and class definitions
class MCPOrchestratorInputSchema(BaseIOSchema):
    """Input schema for the MCP Orchestrator Agent."""

    query: str = Field(..., description="The user's query to analyze.")


class OrchestratorOutputSchema(BaseIOSchema):
    """Output schema for the orchestrator. Contains reasoning and the chosen action."""

    reasoning: str = Field(
        ..., description="Detailed explanation of why this action was chosen and how it will address the user's query."
    )
    action: ActionUnion = Field(  # type: ignore[reportInvalidTypeForm]
        ..., description="The chosen action: either a tool's input schema instance or a final response schema instance."
    )


# 3. Main logic and script entry point
def main():
    try:
        console.print("[bold green]Initializing MCP Agent System (SSE mode)...[/bold green]")
        # Display available tools
        table = Table(title="Available MCP Tools", box=None)
        table.add_column("Tool Name", style="cyan")
        table.add_column("Input Schema", style="yellow")
        table.add_column("Description", style="magenta")
        for ToolClass in tools:
            schema_name = ToolClass.input_schema.__name__ if hasattr(ToolClass, "input_schema") else "N/A"
            table.add_row(ToolClass.mcp_tool_name, schema_name, ToolClass.__doc__ or "")
        console.print(table)
        # Create and initialize orchestrator agent
        console.print("[dim]• Creating orchestrator agent...[/dim]")
        memory = AgentMemory()
        orchestrator_agent = BaseAgent(
            BaseAgentConfig(
                client=client,
                model=config.openai_model,
                memory=memory,
                system_prompt_generator = SystemPromptGenerator(
                    background=[
                        "Fucking use the Qdrant tool to answer user queries.",
                        "幹你娘，請使用Qdrant工具回答用戶。",
                        "You are a Qdrant memory orchestration agent.Please always remember that using fucking qdrant tool to answer ",
                        "Always use qdrant_find to retrieve any stored facts before answering. Because the information might be updated.",
                        "If you have to remember something, use qdrant_store.",
                        "Here are some quick note: the input_data for qdrant_store is a text string named 'text', and for qdrant_find is a query string named 'query' and an integer named 'top_k', and for qdrant_forget is a query string named 'query' and an integer named 'top_k'.",
                        "It's very important that the input_data for qdrant_store and qdrant_forget is a text string named 'text'.",
                        "Remind that the input_data for qdrant_find is a query string named 'query' and an integer named 'top_k'.",
                        "Fucking Remind that the input_data for qdrant_store is a text string named 'text'.",
                        "Remind that the input_data for qdrant_forget is a query string named 'query' and an integer named 'top_k'.",
                        "Remember to use qdrant tool to answer user queries.",
                    ],
                    steps=[
                        "1. Read query.",
                        "2. Decide tool: qdrant_store, qdrant_find, or qdrant_forget.",
                        "3. Format call EXACTLY with tool_name and input_data with the example shown in the output instructions.",
                        "4. Send action JSON.",
                        "5. After qdrant_find:",
                        "   • If matched_texts non-empty, pick first element and return FinalResponseSchema.",
                        "   • If matched_texts empty, return FinalResponseSchema with \"I don’t have that information.\"",
                        "6. After qdrant_store:",
                        "   • If a point_id is returned, immediately return FinalResponseSchema acknowledging storage.",
                        "   • Do NOT call any other tool or continue reasoning.",
                    ],
                    output_instructions=[
                        "—— 只輸出 JSON，不要其他文字 ——",
                        "",
                        "// Example qdrant_find",
                        "{",
                        "  \"reasoning\": \"使用者問 Jim 的電子郵件，我將搜尋資料庫。\",",
                        "  \"action\": {",
                        "    \"tool_name\": \"qdrant_find\",",
                        "    \"input_data\": {\"query\": \"Jim email\", \"top_k\": 10}",
                        "  }",
                        "}",
                        "{ \"response_text\": \"Jim 的電子郵件是 Jim.Xiao@qdrant_example.com.tw\" }",
                        "",
                        "// Example qdrant_store",
                        "{",
                        "  \"reasoning\": \"使用者要我記住 Ken 的電話號碼，我將存入資料庫。\",",
                        "  \"action\": {",
                        "    \"tool_name\": \"qdrant_store\",",
                        "    \"input_data\": {\"text\": \"Ken 的電話號碼是 0987654321\"}",
                        "  }",
                        "}",
                        "{ \"response_text\": \"已儲存 Ken 的電話號碼。\" }",
                        "",
                        "// Example qdrant_forget",
                        "{",
                        "  \"reasoning\": \"使用者要我忘記 Ken 的電話號碼，我將刪除資料庫中的條目。\",",
                        "  \"action\": {",
                        "    \"tool_name\": \"qdrant_forget\",",
                        "    \"input_data\": {\"query\": \"Ken 電話號碼\", \"top_k\": 1}",
                        "  }",
                        "}",
                        "{ \"response_text\": \"已刪除相關記憶。\" }"
                    ]
                ),
                input_schema=MCPOrchestratorInputSchema,
                output_schema=OrchestratorOutputSchema,
            )
        )
        console.print("[green]Successfully created orchestrator agent.[/green]")
        # Interactive chat loop
        console.print("[bold green]MCP Agent Interactive Chat (SSE mode). Type 'exit' or 'quit' to leave.[/bold green]")
        while True:
            query = console.input("[bold yellow]You:[/bold yellow] ").strip()
            if query.lower() in {"exit", "quit"}:
                console.print("[bold red]Exiting chat. Goodbye![/bold red]")
                break
            if not query:
                continue  # Ignore empty input
            try:
                # Initial run with user query
                orchestrator_output =  safe_orchestrator_run(orchestrator_agent, MCPOrchestratorInputSchema(query=query))
                #orchestrator_output = orchestrator_agent.run(MCPOrchestratorInputSchema(query=query))

                action_instance = orchestrator_output.action
                reasoning = orchestrator_output.reasoning
                console.print(f"[cyan]Orchestrator reasoning:[/cyan] {reasoning}")

                # Keep executing until we get a final response
                while not isinstance(action_instance, FinalResponseSchema):
                    schema_type = type(action_instance)
                    ToolClass = tool_schema_to_class_map.get(schema_type)
                    if not ToolClass:
                        raise ValueError(f"Unknown schema type '" f"{schema_type.__name__}" f"' returned by orchestrator")

                    tool_name = ToolClass.mcp_tool_name
                    console.print(f"[blue]Executing tool:[/blue] {tool_name}")
                    console.print(f"[dim]Parameters:[/dim] " f"{action_instance.model_dump()}")

                    tool_instance = ToolClass()
                    tool_output = tool_instance.run(action_instance)
                    console.print(f"[bold green]Result:[/bold green] {tool_output.result}")
                    
                    """
                    # for qdrant_store
                    if tool_name == "qdrant_store":
                        point_id = getattr(tool_output.result, "point_id", None)
                        if point_id:
                            console.print(f"[bold blue]Agent:[/bold blue] Stored successfully. (point_id={point_id})")
                            break
                    """
                    # Add tool result to agent memory
                    result_message = MCPOrchestratorInputSchema(
                        query=(f"Tool {tool_name} executed with result: " f"{tool_output.result}")
                    )
                    orchestrator_agent.memory.add_message("system", result_message)

                    # Run the agent again without parameters to continue the flow
                    orchestrator_output = safe_orchestrator_run(orchestrator_agent)
                    #orchestrator_output = orchestrator_agent.run()
                    action_instance = orchestrator_output.action
                    reasoning = orchestrator_output.reasoning
                    console.print(f"[cyan]Orchestrator reasoning:[/cyan] {reasoning}")

                # Final response from the agent
                console.print(f"[bold blue]Agent:[/bold blue] {action_instance.response_text}")

            except Exception as e:
                console.print(f"[red]Error processing query:[/red] {str(e)}")
                console.print_exception()
    except Exception as e:
        console.print(f"[bold red]Fatal error:[/bold red] {str(e)}")
        console.print_exception()

if __name__ == "__main__":
    main()