[project]
name = "mcp-atomic-agents"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "atomic-agents>=1.1.11",
    "mcp[cli]>=1.11.0",
    "mysql-connector-python>=9.3.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "openai>=1.0.0",
    "instructor>=1.0.0",
]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--tb=short",
    "-v"
]
testpaths = [
    "MySQL",
    "MongoDB"
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "real_db: marks tests that require real database connection",
    "real_llm: marks tests that require real LLM connection"
]
asyncio_mode = "auto"
