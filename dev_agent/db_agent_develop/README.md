# MCP_Atomic_Agents

>參考自 [BrainBlend-AI/atomic-agents](https://github.com/BrainBlend-AI/atomic-agents)

---

# Develop three Database services
1. [MySQL](/MySQL/)
2. [MongoDB](/MongoDB)
3. [Qdrant](/Qdrant/)

# Environment
This repository use uv to create environment
1. Install uv  
https://docs.astral.sh/uv/getting-started/installation/

2. Snyc uv environment with this repository  
    ```bash
    uv venv .venv
    source .venv/bin/activate
    uv pip sync
    ```

# Quick start
After setup uv environment, we can use "uv run xxx.py" to execute the service.

And you can interact with client within the terminal in natural language.  
(English and Traditional Chinese both are available.)

Please remind that run the "server.py" before "client.py".


## MySQL

### Start  Server

```bash
uv run MySQL/server.py
```

### Start Client

```bash
uv run MySQL/client.py
```

## MongoDB

### Start  Server

```bash
uv run MongoDB/server.py
```

### Start Client

```bash
uv run MongoDB/client.py
```

## Qdrant

### Start  Server

```bash
uv run Qdrant/server.py
```

### Start Client

```bash
uv run Qdrant/client.py
```

## Results
1. MySQL  
![demo_MySQL_CRUD](/MySQL/pictures/demo_shorts.png)  

2. MongoDB  
![demo_mongoDB_CRUD](/MongoDB/pictures/demo_mongoDB_CRUD.png)  

3. Qdrant  
![demo_Qdrant_CRUD](/Qdrant/pictures/demo_qdrant_crud.png)  

---

## Model Server
The vLLM server is running Llama-3.3-70B-Instruct on a single NVIDIA H200 GPU at ************:8701 .  

```bash
docker run --runtime nvidia --gpus device=3 \
	--name jim_llama3_3_70B_instruct \
	-v ~/.cache/huggingface:/root/.cache/huggingface \
 	--env "HUGGING_FACE_HUB_TOKEN=hf_key" \
	-p 8701:8000 \
	--ipc=host \
	vllm/vllm-openai:latest \
	--model meta-llama/Llama-3.3-70B-Instruct \
	--tensor-parallel-size 1 \
  --gpu_memory_utilization 0.9 \
	--quantization fp8
```

### Quick health check via curl
```bash
curl -X POST "http://************:8701/v1/chat/completions" \
    -H "Content-Type: application/json" \
    --data '{
        "model": "meta-llama/Llama-3.3-70B-Instruct",
        "messages": [
            {
                "role": "user",
                "content": "Who is the CEO of APPLE?"
            }
        ]
    }'
```