# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a database agent development project that provides MCP (Model Context Protocol) servers for both MySQL and MongoDB interactions using atomic-agents. The project enables natural language queries to be converted into database operations.

## Architecture

The codebase is organized into two main database integration modules:

### MongoDB/ 
- **server.py**: FastMCP server with SSE transport for MongoDB operations
- **client.py**: Interactive terminal client for natural language database queries
- **services/mongoDB_connection.py**: MongoDB connection management
- **tools/**: Individual CRUD operation tools (insert.py, find.py, update.py, delete.py, list_*.py)
- **tools/mongoDB_agents.py**: Consolidated agent tool implementation

### MySQL/
- **server.py**: FastMCP server with SSE transport for MySQL operations  
- **client.py**: Interactive terminal client for natural language database queries
- **services/mysql_connection.py**: MySQL connection management
- **tools/mysql_agents.py**: Main MySQL agent tool (consolidated approach)
- **tools/**: Individual operation tools (crud.py, describe_tables.py, list_tables.py, only_select.py)

## Development Commands

### Environment Setup
```bash
uv venv .venv
source .venv/bin/activate
uv pip sync
```

### Running MongoDB Components
```bash
# Start MongoDB server
uv run MongoDB/server.py

# Start MongoDB client (in separate terminal)
uv run MongoDB/client.py
```

### Running MySQL Components  
```bash
# Start MySQL server
uv run MySQL/server.py

# Start MySQL client (in separate terminal)
uv run MySQL/client.py
```

## External Dependencies

- **LLM Model**: Llama-3.3-70B-Instruct running on vLLM at http://************:8701
- **MongoDB**: Atlas deployment "JimTrial" accessible via mongosh
- **MySQL**: Local MySQL instance with web interface at http://************:8080
- **MCP Server**: Uses FastMCP with SSE transport for real-time communication

## Key Implementation Details

Both MongoDB and MySQL implementations follow the atomic-agents pattern with:
- Tool-based architecture using pydantic models for input/output validation
- SSE (Server-Sent Events) transport for real-time client-server communication
- Natural language processing to convert user queries into database operations
- Comprehensive CRUD operation support through specialized tool classes

## MySQL Client Call Stack Analysis

### Entry Point - Script Execution

**Call Stack Entry**: `python MySQL/client.py` → `main()` (MySQL/client.py:133)

```python
if __name__ == "__main__":
    main()
```

### Initialization Phase

#### Configuration Setup (MySQL/client.py:19-39)
```python
@dataclass
class MCPConfig:
    mcp_server_url: str = "http://localhost:8702"  # MCP server endpoint
    openai_model: str = "meta-llama/Llama-3.3-70B-Instruct"

config = MCPConfig()
client = instructor.from_openai(openai.OpenAI(base_url=vLLM_url, api_key="OPENAI_API_KEY"), mode=instructor.Mode.JSON)
```

#### Tool Fetching (MySQL/client.py:49-67)
```python
# Fetches available MCP tools from the server
tools = fetch_mcp_tools(
    mcp_endpoint=config.mcp_server_url,  # http://localhost:8702
    use_stdio=False,
)

# Creates mapping from schema to tool class
tool_schema_to_class_map: Dict[Type[BaseIOSchema], Type[BaseAgent]] = {
    ToolClass.input_schema: ToolClass for ToolClass in tools
}

# Defines union of all available action schemas
ActionUnion = Union[*available_schemas]
```

**Core Logic**: The client connects to the MCP server running on port 8702 and dynamically fetches available tools (primarily `mysql_agent`).

#### Agent Creation (MySQL/client.py:147-244)
```python
memory = AgentMemory()
orchestrator_agent = BaseAgent(
    BaseAgentConfig(
        client=client,  # LLM client (Llama-3.3-70B)
        model=config.openai_model,
        memory=memory,
        system_prompt_generator=SystemPromptGenerator(...),
        input_schema=MCPOrchestratorInputSchema,
        output_schema=OrchestratorOutputSchema,
    )
)
```

**Core Logic**: Creates a `BaseAgent` with comprehensive system prompts that instruct it how to:
- Route queries to appropriate tools
- Handle SQL validation and error recovery
- Manage material shortage analysis workflow
- Always output both `reasoning` and `action` fields

### Interactive Chat Loop

#### User Input Processing (MySQL/client.py:248-257)
```python
while True:
    query = console.input("[bold yellow]You:[/bold yellow] ").strip()
    if query.lower() in {"exit", "quit"}:
        break
    
    # Initial orchestrator run
    orchestrator_output = safe_orchestrator_run(
        orchestrator_agent, 
        MCPOrchestratorInputSchema(query=query)
    )
```

#### Safe Orchestrator Execution (MySQL/client.py:72-112)
```python
def safe_orchestrator_run(agent, *args, **kwargs):
    max_retry = 20
    for i in range(max_retry):
        try:
            output = agent.run(*args, **kwargs)
            if hasattr(output, "action") and output.action is not None:
                return output
            else:
                # Add error message to memory and retry
                agent.memory.add_message("system", {...})
        except Exception as e:
            # Handle validation errors and retry
            agent.memory.add_message("system", {...})
```

**Core Logic**: Provides robust error handling with retry mechanism for LLM output validation issues.

### Tool Execution Flow

#### Action Processing Loop (MySQL/client.py:264-291)
```python
while not isinstance(action_instance, FinalResponseSchema):
    schema_type = type(action_instance)
    ToolClass = tool_schema_to_class_map.get(schema_type)
    
    tool_name = ToolClass.mcp_tool_name  # "mysql_agent"
    tool_instance = ToolClass()
    tool_output = tool_instance.run(action_instance)
    
    # Add result to agent memory
    result_message = MCPOrchestratorInputSchema(
        query=f"Tool {tool_name} executed with result: {tool_output.result}"
    )
    orchestrator_agent.memory.add_message("system", result_message)
    
    # Continue orchestration
    orchestrator_output = safe_orchestrator_run(orchestrator_agent)
    action_instance = orchestrator_output.action
```

#### MySQL Tool Execution
**Server Side** (MySQL/tools/mysql_agents.py:26-43):
```python
async def execute(self, input_data: MySQLAgentInput) -> ToolResponse:
    service = MySQL_Service()
    conn = service.get_conn()  # Connect to MySQL at ************
    cursor = conn.cursor()
    cursor.execute(input_data.query)  # Execute SQL
    
    if cursor.description:
        result = cursor.fetchall()  # SELECT queries
    else:
        result = f"Affected rows: {cursor.rowcount}"  # INSERT/UPDATE/DELETE
    
    return ToolResponse.from_model(MySQLAgentOutput(result=result))
```

**Database Connection** (MySQL/services/mysql_connection.py:12-21):
```python
def get_conn(self):
    return mysql.connector.connect(
        host="************",
        user="root", 
        password="password",
        database="trialDB_01",
        autocommit=True,
        charset="utf8mb4",
    )
```

### Core Architecture Flow

```
User Query → Orchestrator Agent → Tool Selection → MySQL Tool → Database → Result → Agent Memory → Continue/Finalize
```

#### Key Components:

1. **Orchestrator Agent**: Uses Llama-3.3-70B to analyze queries and route to appropriate tools
2. **MCP Protocol**: Uses Server-Sent Events (SSE) for real-time client-server communication
3. **Tool System**: Dynamic tool loading with schema validation
4. **Memory Management**: Maintains conversation context and tool execution history
5. **Error Recovery**: Robust retry mechanisms with format validation

#### Specialized Logic:

- **Material Shortage Analysis**: Built-in workflow for inventory checking and email notifications
- **SQL Validation**: Always checks table schemas before executing queries
- **Multi-step Processing**: Can chain multiple tool calls before providing final response

The system implements a sophisticated agent-based architecture where natural language queries are intelligently routed through an LLM orchestrator to execute database operations via MCP tools.

Always use:
- serena for semantic code retrieval and editing tools
- context7 for up to date documentation on third party code
- sequential thinking for any decision making
Read the claude.md root file before you do anything.
#$ARGUMENTS