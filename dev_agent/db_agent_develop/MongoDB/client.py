# pyright: reportInvalidTypeForm=false
from atomic_agents.lib.factories.mcp_tool_factory import fetch_mcp_tools
from rich.console import Console
from rich.table import Table
import openai
import os
import instructor
from pydantic import Field
from atomic_agents.agents.base_agent import BaseIOSchema, BaseAgent, BaseAgentConfig
from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator
from atomic_agents.lib.components.agent_memory import AgentMemory
from typing import Union, Type, Dict, Optional
from dataclasses import dataclass

model_name = "meta-llama/Llama-3.3-70B-Instruct"
vLLM_url = "http://************:8701/v1"

# 1. Configuration and environment setup
@dataclass
class MCPConfig:
    """Configuration for the MCP Agent system using SSE transport."""

    mcp_server_url: str = "http://localhost:8705"

    # NOTE: In contrast to other examples, we use gpt-4o and not gpt-4o-mini here.
    # In my tests, gpt-4o-mini was not smart enough to deal with multiple tools like that
    # and at the moment MCP does not yet allow for adding sufficient metadata to
    # clarify tools even more and introduce more constraints.
    openai_model: str = "meta-llama/Llama-3.3-70B-Instruct"
    openai_api_key: str = os.getenv("OPENAI_API_KEY")
    """
    def __post_init__(self):
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
    """

config = MCPConfig()
console = Console()
client = instructor.from_openai(openai.OpenAI(base_url=vLLM_url, api_key="OPENAI_API_KEY"), mode=instructor.Mode.JSON)


class FinalResponseSchema(BaseIOSchema):
    """Schema for providing a final text response to the user."""

    response_text: str = Field(..., description="The final text response to the user's query")


# Fetch tools and build ActionUnion statically
tools = fetch_mcp_tools(
    mcp_endpoint=config.mcp_server_url,
    use_stdio=False,
)
if not tools:
    raise RuntimeError("No MCP tools found. Please ensure the MCP server is running and accessible.")

# Build mapping from input_schema to ToolClass
tool_schema_to_class_map: Dict[Type[BaseIOSchema], Type[BaseAgent]] = {
    ToolClass.input_schema: ToolClass for ToolClass in tools if hasattr(ToolClass, "input_schema")
}
# Collect all tool input schemas
tool_input_schemas = tuple(tool_schema_to_class_map.keys())
print("[Debug]")
print("Tool input schemas:", [schema.__name__ for schema in tool_input_schemas])
# Available schemas include all tool input schemas and the final response schema
available_schemas = tool_input_schemas + (FinalResponseSchema,)
print("[Debug]")
print("Available schemas:", [schema.__name__ for schema in available_schemas])

# Define the Union of all action schemas
ActionUnion = Union[*available_schemas]

def safe_orchestrator_run(agent, *args, **kwargs):
    max_retry = 20

    example_schema = {
        "reasoning": "To insert a new employee, I will use mongo_insert.",
        "action": {
            "tool_name": "mongo_insert",
            "input_data": {
                "collection": "employees",
                "document": {
                    "name": "Jim Xiao",
                    "email": "Jim.Xiao@example_mongo.com",
                    "age": 25
                }
            }
        }
    }

    for i in range(max_retry):
        try:
            output = agent.run(*args, **kwargs)
            if hasattr(output, "action") and output.action is not None:
                return output
            else:
                agent.memory.add_message(
                    "system",
                    {"query": (
                        "【格式錯誤提醒】你的輸出缺少 'action' 欄位，請重新產生有效 JSON。\n"
                        "請依照以下 JSON 格式回覆，包含 reasoning 與 action：\n"
                        f"{example_schema}\n"
                        "action 的值可以是 mongo_insert、mongo_find 等工具的 input schema，或 FinalResponseSchema。\n"
                        "請修正格式並重新輸出。"
                    )}
                )
        except Exception as e:
            agent.memory.add_message(
                "system",
                {"query": (
                    "【Validation Error】你的上一輪回覆產生了錯誤，請檢查輸出格式！\n"
                    f"錯誤內容如下：{str(e)}\n"
                    "請依照以下格式修正：\n"
                    f"{example_schema}\n"
                )}
            )
    raise RuntimeError("LLM failed to output correct schema after multiple retries.")

# 2. Schema and class definitions
class MCPOrchestratorInputSchema(BaseIOSchema):
    """Input schema for the MCP Orchestrator Agent."""

    query: str = Field(..., description="The user's query to analyze.")


class OrchestratorOutputSchema(BaseIOSchema):
    """Output schema for the orchestrator. Contains reasoning and the chosen action."""

    reasoning: str = Field(
        ..., description="Detailed explanation of why this action was chosen and how it will address the user's query."
    )
    action: ActionUnion = Field(  # type: ignore[reportInvalidTypeForm]
        ..., description="The chosen action: either a tool's input schema instance or a final response schema instance."
    )


# 3. Main logic and script entry point
def main():
    try:
        console.print("[bold green]Initializing MCP Agent System (SSE mode)...[/bold green]")
        # Display available tools
        table = Table(title="Available MCP Tools", box=None)
        table.add_column("Tool Name", style="cyan")
        table.add_column("Input Schema", style="yellow")
        table.add_column("Description", style="magenta")
        for ToolClass in tools:
            schema_name = ToolClass.input_schema.__name__ if hasattr(ToolClass, "input_schema") else "N/A"
            table.add_row(ToolClass.mcp_tool_name, schema_name, ToolClass.__doc__ or "")
        console.print(table)
        # Create and initialize orchestrator agent
        console.print("[dim]• Creating orchestrator agent...[/dim]")
        memory = AgentMemory()
        orchestrator_agent = BaseAgent(
            BaseAgentConfig(
                client=client,
                model=config.openai_model,
                memory=memory,
                system_prompt_generator=SystemPromptGenerator(
                    background=[
                        "You are a MongoDB Orchestrator Agent. Use the available tools to fulfill user requests in natural language.",
                        "Always output valid JSON with both 'reasoning' and 'action' fields.",
                        "Each tool has a specific input schema. Refer to the descriptions to determine required fields.",
                        "You must NEVER guess schema fields. If information is missing or ambiguous, ask the user to clarify.",
                        "Always use FinalResponseSchema to conclude interaction with a summary or message.",
                    ],
                    steps=[
                        "1. Read user query and understand the intent.",
                        "2. Select the appropriate MongoDB tool among: mongo_insert, mongo_find, mongo_update, mongo_delete, mongo_list_dbs, mongo_list_collections.",
                        "3. Provide the exact input schema required by the selected tool.",
                        "4. Chain multiple tool results if needed to fulfill complex queries.",
                        "5. When finished, use FinalResponseSchema to respond to the user with a final message.",
                    ],
                    output_instructions=[
                        "When using mongo_insert, you MUST provide:",
                        "- collection: the MongoDB collection name (e.g., 'employees')",
                        "- document: a JSON object with key-value pairs to insert.",
                        "",
                        "When using mongo_find, provide:",
                        "- collection: the target collection.",
                        "- query: a MongoDB query object (e.g., {\"name\": \"Jim Xiao\"})",
                        "",
                        "When using mongo_update, provide:",
                        "- collection",
                        "- query: filter condition (e.g., {\"name\": \"Jim Xiao\"})",
                        "- update: fields to modify, using the $set syntax (e.g., {\"$set\": {\"age\": 30}})",
                        "",
                        "When using mongo_delete, provide:",
                        "- collection",
                        "- query: condition for documents to delete.",
                        "",
                        "Use FinalResponseSchema only after all steps are complete. Example:",
                        "{ \"response_text\": \"Successfully inserted Jim Xiao into employees.\" }"
                    ]
                ),
                input_schema=MCPOrchestratorInputSchema,
                output_schema=OrchestratorOutputSchema,
            )
        )
        console.print("[green]Successfully created orchestrator agent.[/green]")
        # Interactive chat loop
        console.print("[bold green]MCP Agent Interactive Chat (SSE mode). Type 'exit' or 'quit' to leave.[/bold green]")
        while True:
            query = console.input("[bold yellow]You:[/bold yellow] ").strip()
            if query.lower() in {"exit", "quit"}:
                console.print("[bold red]Exiting chat. Goodbye![/bold red]")
                break
            if not query:
                continue  # Ignore empty input
            try:
                # Initial run with user query
                orchestrator_output =  safe_orchestrator_run(orchestrator_agent, MCPOrchestratorInputSchema(query=query))
                #orchestrator_output = orchestrator_agent.run(MCPOrchestratorInputSchema(query=query))

                action_instance = orchestrator_output.action
                reasoning = orchestrator_output.reasoning
                console.print(f"[cyan]Orchestrator reasoning:[/cyan] {reasoning}")

                # Keep executing until we get a final response
                while not isinstance(action_instance, FinalResponseSchema):
                    schema_type = type(action_instance)
                    ToolClass = tool_schema_to_class_map.get(schema_type)
                    if not ToolClass:
                        raise ValueError(f"Unknown schema type '" f"{schema_type.__name__}" f"' returned by orchestrator")

                    tool_name = ToolClass.mcp_tool_name
                    console.print(f"[blue]Executing tool:[/blue] {tool_name}")
                    console.print(f"[dim]Parameters:[/dim] " f"{action_instance.model_dump()}")

                    tool_instance = ToolClass()
                    tool_output = tool_instance.run(action_instance)
                    console.print(f"[bold green]Result:[/bold green] {tool_output.result}")

                    # Add tool result to agent memory
                    result_message = MCPOrchestratorInputSchema(
                        query=(f"Tool {tool_name} executed with result: " f"{tool_output.result}")
                    )
                    orchestrator_agent.memory.add_message("system", result_message)

                    # Run the agent again without parameters to continue the flow
                    orchestrator_output = safe_orchestrator_run(orchestrator_agent)
                    #orchestrator_output = orchestrator_agent.run()
                    action_instance = orchestrator_output.action
                    reasoning = orchestrator_output.reasoning
                    console.print(f"[cyan]Orchestrator reasoning:[/cyan] {reasoning}")

                # Final response from the agent
                console.print(f"[bold blue]Agent:[/bold blue] {action_instance.response_text}")

            except Exception as e:
                console.print(f"[red]Error processing query:[/red] {str(e)}")
                console.print_exception()
    except Exception as e:
        console.print(f"[bold red]Fatal error:[/bold red] {str(e)}")
        console.print_exception()

if __name__ == "__main__":
    main()