from typing import Dict, Any
from pydantic import Field, BaseModel

from interfaces.tool import Tool, BaseToolInput, ToolResponse, ToolContent
from services.mongoDB_connection import MongoDB_Service

class MongoUpdateInput(BaseToolInput):
    collection: str = Field(..., description="Collection name to update")
    query: Dict[str, Any] = Field(..., description="Filter to match documents")
    update: Dict[str, Any] = Field(..., description="Update operations to apply")

    class Config:
        schema_extra = {
            "examples": [
                {
                    "collection": "employees",
                    "query": {"name": "<PERSON>"},
                    "update": {"$set": {"age": 15}}
                }
            ]
        }

class MongoUpdateOutput(BaseModel):
    matched_count: int = Field(..., description="Number of matched documents")
    modified_count: int = Field(..., description="Number of documents actually modified")

class MongoUpdateTool(Tool):
    name = "mongo_update"
    description = "Update documents in a MongoDB collection"
    input_model = MongoUpdateInput
    output_model = MongoUpdateOutput

    async def execute(self, input_data: MongoUpdateInput) -> ToolResponse:
        client = MongoDB_Service().get_conn()
        db = client.get_default_database()
        if db is None:
            db = client["jimTrial"]

        collection = db[input_data.collection]
        result = collection.update_many(input_data.query, input_data.update)

        return ToolResponse.from_model(
            MongoUpdateOutput(
                matched_count=result.matched_count,
                modified_count=result.modified_count
            )
        )
