from typing import Dict, Any
from pydantic import Field, BaseModel

from interfaces.tool import Tool, BaseToolInput, ToolResponse
from services.mongoDB_connection import MongoDB_Service

class MongoDeleteInput(BaseToolInput):
    collection: str = Field(..., description="Collection to delete from")
    filter: Dict[str, Any] = Field(..., description="Filter to match document to delete")

class MongoDeleteOutput(BaseModel):
    deleted: int = Field(..., description="Number of documents deleted")

class MongoDeleteTool(Tool):
    name = "mongo_delete"
    description = "Delete a document from MongoDB"
    input_model = MongoDeleteInput
    output_model = MongoDeleteOutput

    def execute(self, input_data: MongoDeleteInput) -> ToolResponse:
        client = MongoDB_Service().get_conn()
        db = client.get_default_database()
        if db is None:
            db = client["jimTrial"]
            
        result = db[input_data.collection].delete_one(input_data.filter)
        return MongoDeleteOutput(deleted=result.deleted_count)
