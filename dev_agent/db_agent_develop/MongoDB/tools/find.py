import json
from typing import Dict, Any, Optional, List
from pydantic import Field, BaseModel, ConfigDict

from interfaces.tool import Tool, BaseToolInput, ToolResponse
from services.mongoDB_connection import MongoDB_Service

class MongoFindInput(BaseToolInput):
    collection: str = Field(..., description="Collection name to query")
    filter: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Filter for query")
    limit: int = Field(10, description="Limit number of documents")
    model_config = ConfigDict(extra="allow")

class MongoFindOutput(BaseModel):
    documents: List[Dict[str, Any]] = Field(..., description="Query results")

class MongoFindTool(Tool):
    name = "mongo_find"
    description = "Find documents in a MongoDB collection"
    input_model = MongoFindInput
    output_model = MongoFindOutput

    async def execute(self, input_data: MongoFindInput) -> ToolResponse:
        client = MongoDB_Service().get_conn()
        db = client.get_default_database()
        if db is None:
            db = client["jimTrial"]

        cursor = db[input_data.collection].find(input_data.query)
        documents = [doc async for doc in cursor] if hasattr(cursor, "__aiter__") else list(cursor)

        for doc in documents:
            doc["_id"] = str(doc["_id"])

        return ToolResponse.from_model(
            MongoFindOutput(results=documents)
        )
