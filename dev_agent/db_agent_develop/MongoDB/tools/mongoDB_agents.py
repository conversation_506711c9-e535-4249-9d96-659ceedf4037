"""Tool for adding two numbers."""

from typing import Dict, Any, Union
from pydantic import Field, BaseModel, ConfigD<PERSON>
from typing import Optional, Literal, Dict, Any, List, Union

from interfaces.tool import Tool, BaseToolInput, ToolResponse
from services.mongoDB_connection import MongoDB_Service

class MongoAgentInput(BaseToolInput):
    """Input schema for mongo_agent"""

    action: Literal["list_dbs", "list_collections", "find", "insert", "update", "delete"] = Field(
        ..., description="The MongoDB action to perform"
    )
    collection: Optional[str] = Field(None, description="The collection name to operate on")
    filter: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Query filter for find/update/delete")
    document: Optional[Dict[str, Any]] = Field(None, description="Document to insert")
    update_data: Optional[Dict[str, Any]] = Field(None, description="Update fields for update action")
    limit: Optional[int] = Field(10, description="Limit the number of documents to return in find")

class MongoAgentOutput(BaseModel):
    """Output schema for mongo_agent"""

    success: bool = Field(..., description="Whether the operation was successful")
    data: Optional[Union[List[Dict[str, Any]], Dict[str, Any]]] = Field(
        None, description="Data returned from the MongoDB operation"
    )
    error: Optional[str] = Field(None, description="Error message, if any")

class MongoAgentTool(Tool):
    name = "mongo_agent"
    description = "Dynamic MongoDB CRUD tool"
    input_model = MongoAgentInput
    output_model = MongoAgentOutput

    async def execute(self, input_data: MongoAgentInput) -> ToolResponse:
        """Execute the MongoDB action and return the results."""
        service = MongoDB_Service()
        client = service.get_conn()
        db = client["jimTrial"]

        try:
            action = input_data.action
            if action == "list_dbs":
                result = client.list_database_names()
            elif action == "list_collections":
                if not input_data.collection:
                    raise ValueError("collection required")
                result = db[input_data.collection].list_collection_names()
            elif action == "find":
                docs = db[input_data.collection].find(input_data.filter or {}).limit(input_data.limit)
                result = list(docs)
            elif action == "insert":
                res = db[input_data.collection].insert_one(input_data.document or {})
                result = {"inserted_id": str(res.inserted_id)}
            elif action == "update":
                res = db[input_data.collection].update_one(input_data.filter or {}, {"$set": input_data.update_data or {}})
                result = {
                    "matched": res.matched_count,
                    "modified": res.modified_count,
                    "upserted_id": str(res.upserted_id) if res.upserted_id else None
                }
            elif action == "delete":
                res = db[input_data.collection].delete_one(input_data.filter or {})
                result = {"deleted": res.deleted_count}
            else:
                raise ValueError(f"Unknown action {action}")

            return MongoAgentOutput(success=True, data=result, error=None)
        except Exception as e:
            return MongoAgentOutput(success=False, data=None, error=str(e))
