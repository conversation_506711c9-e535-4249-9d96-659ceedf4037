from pydantic import Field, BaseModel

from interfaces.tool import Tool, BaseToolInput, ToolResponse
from services.mongoDB_connection import MongoDB_Service


class MongoListCollectionsInput(BaseToolInput):
    database: str = Field(..., description="Database name to list collections from")

class MongoListCollectionsOutput(BaseModel):
    collections: list[str] = Field(..., description="List of collection names")

class MongoListCollectionsTool(Tool):
    name = "mongo_list_collections"
    description = "List all collections in a MongoDB database"
    input_model = MongoListCollectionsInput
    output_model = MongoListCollectionsOutput

    def execute(self, input_data: MongoListCollectionsInput) -> ToolResponse:
        client = MongoDB_Service().get_conn()
        target_db = client[input_data.database]
        cols = target_db.list_collection_names()
        return MongoListCollectionsOutput(collections=cols)
