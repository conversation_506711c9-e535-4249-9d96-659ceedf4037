from pydantic import Field, BaseModel

from interfaces.tool import Tool, BaseToolInput, ToolResponse
from services.mongoDB_connection import MongoDB_Service

class MongoListDBsInput(BaseToolInput):
    pass

class MongoListDBsOutput(BaseModel):
    databases: list[str] = Field(..., description="List of database names")

class MongoListDBsTool(Tool):
    name = "mongo_list_dbs"
    description = "List all MongoDB databases"
    input_model = MongoListDBsInput
    output_model = MongoListDBsOutput

    def execute(self, input_data: MongoListDBsInput) -> ToolResponse:
        client = MongoDB_Service().get_conn()
        dbs = client.list_database_names()
        return MongoListDBsOutput(databases=dbs)
