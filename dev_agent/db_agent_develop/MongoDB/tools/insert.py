from typing import Dict, Any
from pydantic import Field, BaseModel

from interfaces.tool import Tool, BaseToolInput, ToolResponse, ToolContent
from services.mongoDB_connection import MongoDB_Service

class MongoInsertInput(BaseToolInput):
    collection: str = Field(..., description="Collection to insert into")
    document: Dict[str, Any] = Field(..., description="Document to insert")

    class Config:
        schema_extra = {
            "examples": [
                {
                    "collection": "employees",
                    "document": {
                        "name": "<PERSON>",
                        "email": "Jim<PERSON>Xiao@example_mongo.com",
                        "age": 25
                    }
                }
            ]
        }

class MongoInsertOutput(BaseModel):
    inserted_id: str = Field(..., description="ID of inserted document")

class MongoInsertTool(Tool):
    name = "mongo_insert"
    description = "Insert a document into a MongoDB collection"
    input_model = MongoInsertInput
    output_model = MongoInsertOutput

    async def execute(self, input_data: MongoInsertInput) -> ToolResponse:
        client = MongoDB_Service().get_conn()
        db = client.get_default_database()
        if db is None:
            db = client["jimTrial"]

        result = db[input_data.collection].insert_one(input_data.document)

        output_model_instance = MongoInsertOutput(inserted_id=str(result.inserted_id))
        return ToolResponse.from_model(output_model_instance)
