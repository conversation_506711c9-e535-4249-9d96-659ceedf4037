from pymongo import MongoClient
import os

class MongoDB_Service:
    """Connect to MongoDB database."""
    def __init__(self):
        self.uri = os.getenv("MONGO_URI", "mongodb://10.101.8.110:27017/jimTrial?directConnection=true")

    def get_conn(self):
        """Return a MongoClient connection."""
        client = MongoClient(self.uri)
        return client

"""
MONGO_URI = os.getenv(
    "MONGO_URI",
    "mongodb://10.101.8.110:27017/jimTrial?directConnection=true"
)

client = MongoClient(MONGO_URI)
db = client["jimTrial"]
"""