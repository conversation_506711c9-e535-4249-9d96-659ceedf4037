# mcp_atomic_agents

參考自 [BrainBlend-AI/atomic-agents](https://github.com/BrainBlend-AI/atomic-agents)

---

## 目錄架構
```plaintext
MongoDB/
├── server.py                   # Implementation for SSE transport
├── client.py                   # User chat with agent here
├── interfaces/                 # Base classes/interfaces for tools and resources
│   └── tool.py
├── services/
│   └── mongoDB_connection.py   # Connect to local MongoDB
├── tools/
│   ├── delete.py               # delete_one
│   ├── find.py                 # find
│   ├── insert.py               # insert_one
│   ├── list_collections.py     # list_collection_names
│   ├── list_dbs.py             # list_database_names
│   ├── mongoDB_agents.py       # Implement all CRUD tools
│   └── update.py               # update_many
├── resources/
├── pictures/
│   ├── demo_mongoDB_CRUD.png   # demo_CRUD_scenario
│   ├── mongoDB_initial.png     # the initial MongoDB
│   ├── mongoDB_add.png         # the MongoDB after add data
│   └── mongoDB_update.png      # the MongoDB after update data
└── README.md

```

## MongoDB with mongosh
Create the MongoDB service on L4*2 server.(10.101.8.110)

- Establish MongoDB's deployment
    ``` shell
    atlas deployments setup
    -> local - Local Database
    -> custom - With custom settings
    -> (Deployment Name) JimTrial
    -> MongoDB version 8.0
    -> specify a port(MongoDB's default port is 27027)
    ```
- Connect to deployment
    ``` shell
    atlas deployments connect
    -> (Select a deployment) JimTrial
    -> (How would you like to connect to JimTrial) mongosh
    ```
- Use mongosh(Atlas-cli) to interact with MongoDB
    ```
    show dbs
    use jimTrial
    db.createCollection("employees")
    db.employees.insertOne({
        name: "Jim Xiao",
        email: "Jim.Xiao@example_mongo.com",
        age: 25
    })
    show collections
    db.employees.find({})
    db.employees.deleteOne( { _id: ObjectId("688c81330580703716ce9f29") } )
    ```

## Quick Setup

### Use uv to setup environment
After install uv
```bash
uv venv .venv
source .venv/bin/activate
uv pip sync
```

### Start  Server

```bash
uv run MongoDB/server.py
```

### Start Client

```bash
uv run MongoDB/client.py
```

### 開始問問題

- 啟動 `client.py` 後，即可直接在 terminal 輸入自然語言問問題.  
---

## Results  
1. Interact with MongoDB  
    ### Entire Conversation:
    ![demo_mongoDB_CRUD](/MongoDB/pictures/demo_mongoDB_CRUD.png)  

    ### Snapshots of MongoDB State After Each Operation:
    ![mongoDB_initial](/MongoDB/pictures/mongoDB_initial.png)  
    ``` code
    請幫我新增一位員工資料，名字叫 Jim Xiao，email 是 Jim.Xiao@example_mongo.com，年齡 25 歲。
    ```
    ![mongoDB_add](/MongoDB/pictures/mongoDB_add.png)
    ``` code
    請幫我把 Jim Xiao 的年齡改成 10 歲。
    ```
    ![mongoDB_update](/MongoDB/pictures/mongoDB_update.png)

