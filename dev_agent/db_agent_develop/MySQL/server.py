import sys
import uvicorn
from typing import List

from mcp.server.fastmcp import FastMC<PERSON>
from mcp.server import Server
from mcp.server.sse import SseServerTransport

from starlette.applications import Starlette
from starlette.requests import Request
from starlette.routing import Route, Mount
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

# import database tools
"""
from tools.list_tables import ListTablesTool, ListTablesInput, ListTablesOutput
from tools.describe_tables import DescribeTableTool, DescribeTableInput, DescribeTableOutput
from tools.only_select import OnlySelectTool, OnlySelectInput, OnlySelectOutput
from tools.crud import CRUDTool, CRUDInput, CRUDOutput
"""
from tools.mysql_agents import MyS<PERSON>AgentTool, MySQLAgentInput, MySQLAgentOutput
from interfaces.tool import Tool, BaseToolInput, ToolResponse, ToolContent

# print(MySQLAgentInput.model_json_schema())

def get_available_tools() -> List[Tool]:
    """'Get list of all available tools.'"""
    return [MySQLAgentTool()]
    #return [ListTablesTool(), DescribeTableTool(), OnlySelectTool(), CRUDTool()]

def create_app(mcp_server: Server) -> Starlette:
    # Create SSE transport
    sse = SseServerTransport("/messages/")

    # Define GET /sse 的 handler
    async def handle_sse(request: Request) -> None:
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            await mcp_server.run(
                read_stream,
                write_stream,
                mcp_server.create_initialization_options(),
            )

    # CORS middleware
    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
            allow_credentials=True,
        )
    ]

    # Starlette Route：GET /sse, POST /messages
    return Starlette(
        routes=[
            Route("/sse", handle_sse, methods=["GET"]),
            Mount("/messages/", app=sse.handle_post_message),
        ],
        middleware=middleware,
    )

# 用 FastMCP 建立 MCP server
mcp = FastMCP("mysql_sse_server", log_level="INFO")
for tool in get_available_tools():
    mcp.add_tool(
        fn=tool.execute,
        name=tool.name,
        description=tool.__doc__,           # docstring
        structured_output=tool.output_model, # Output Schema
    )
# print(MySQLAgentInput.schema_json())

# 宣告 Starlette app
app = create_app(mcp._mcp_server)

if __name__ == "__main__":
    print("🚀 SSE MCP Server ready at http://127.0.0.1:8703/sse", file=sys.stderr)
    uvicorn.run(
        "server:app",
        host="127.0.0.1",
        port=8702,
        reload=False,
        log_level="info",
    )