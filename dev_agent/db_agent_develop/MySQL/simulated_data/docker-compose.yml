# docker-compose.yml
version: "3.8"

services:
  db:
    image: mysql:8.0
    container_name: jim_mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
    volumes:
      - ./db/create_dbs.sql:/docker-entrypoint-initdb.d/create_dbs.sql:ro
      - ./db/simulated_data.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: jim_mysql_web
    depends_on:
      - db
    volumes:
      - ./web:/var/www/html
    ports:
      - "8080:80"

