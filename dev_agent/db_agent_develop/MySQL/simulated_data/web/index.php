<?php
/* ------------- Connection ------------- */
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
header('Content-Type:text/html; charset=utf-8');

$db = new mysqli('db','root','password','trialDB_01');
$db->set_charset('utf8mb4');

/* ------------- 列出所有 table ------------- */
$tables = array_column($db->query("SHOW TABLES")->fetch_all(),0);
$tbl    = $_GET['t'] ?? $tables[0];

/* ------------- 列出 tabel 的 schema ------------- */
$cols = $db->query("DESCRIBE `$tbl`")->fetch_all(MYSQLI_ASSOC);
$pk   = array_values(array_filter($cols,fn($c)=>$c['Key']==='PRI'))[0]['Field'];
$auto = array_column(array_filter($cols,fn($c)=>$c['Extra']==='auto_increment'),'Field');

/* ------------- CRUD  ------------- */
if ($_SERVER['REQUEST_METHOD']==='POST' && isset($_POST['_act'])) {
    $act = $_POST['_act']; unset($_POST['_act']);

    /* delete */
    if ($act==='del'){
        $stmt=$db->prepare("DELETE FROM `$tbl` WHERE `$pk`=?");
        $stmt->bind_param('s',$_POST[$pk]); $stmt->execute();
    }

    /* update */
    elseif ($act==='upd'){
        $sets=$vals=[];
        foreach($_POST as $k=>$v){
            if($k===$pk||in_array($k,$auto)) continue;
            $sets[]="`$k`=?"; $vals[]=$v;
        }
        $vals[]=$_POST[$pk];
        if($sets){
            $stmt=$db->prepare("UPDATE `$tbl` SET ".join(',',$sets)." WHERE `$pk`=?");
            $stmt->bind_param(str_repeat('s',count($vals)), ...$vals);
            $stmt->execute();
        }
    }

    /* insert */
    elseif ($act==='add'){
        $fields=$place=$vals=[];
        foreach($_POST as $k=>$v){
            if($v===''||in_array($k,$auto)) continue;
            $fields[]="`$k`"; $place[]='?'; $vals[]=$v;
        }
        if($fields){
            $sql="INSERT INTO `$tbl` (".join(',',$fields).") VALUES (".join(',',$place).")";
            $stmt=$db->prepare($sql);
            $stmt->bind_param(str_repeat('s',count($vals)), ...$vals);
            $stmt->execute();
        }
    }
    header("Location:?t=$tbl"); exit;
}

/* ------------- select ------------- */
$rows = $db->query("SELECT * FROM `$tbl`")->fetch_all(MYSQLI_ASSOC);

/* ------------- 輸入欄位 ------------- */
function fieldInput($col, $val='', $readonly=false){
    $f = $col['Field']; $type = $col['Type'];
    if ($readonly) return htmlspecialchars($val)."<input type='hidden' name='$f' value='".htmlspecialchars($val)."'>";
    if (stripos($type,'text')!==false)
        return "<textarea name='$f'>".htmlspecialchars($val)."</textarea>";
    if (preg_match('/datetime|timestamp/i',$type))
        return "<input type='datetime-local' name='$f' value=\"".( $val?str_replace(' ','T',$val):'' )."\">";
    if (preg_match('/date/i',$type))
        return "<input type='date' name='$f' value=\"$val\">";
    return "<input name='$f' value=\"".htmlspecialchars($val)."\">";
}
?>
<!DOCTYPE html><html lang="zh-Hant"><head>
<meta charset="utf-8">
<title><?=$tbl?></title>
<style>
body{font-family:system-ui,"Noto Sans TC",sans-serif;padding:16px}
table{border-collapse:collapse;width:100%}
th,td{border:1px solid #aaa;padding:4px 6px;font-size:14px}
tr:nth-child(even) td:not(.newrow td){background:#f8f8f8}
.newrow{background:#e8f7e8}
input,textarea{width:100%;box-sizing:border-box;font-size:14px}
.btn{padding:2px 6px;margin:0 2px}
</style></head><body>

<h2>資料表管理 – <?=$tbl?></h2>
<form style="margin-bottom:8px">
<label>選擇表格：
  <select name="t" onchange="this.form.submit()">
    <?php foreach($tables as $t) echo "<option".($t==$tbl?' selected':'').">$t</option>";?>
  </select>
</label>
</form>

<table>
<!-- -------- header -------- -->
<thead><tr>
  <?php foreach($cols as $c) echo "<th>{$c['Field']}</th>";?>
  <th>動作</th>
</tr></thead>

<tbody>
<!-- -------- for 新增資料 -------- -->
<tr class="newrow">
<form method="post">
<?php foreach($cols as $c): ?>
  <td><?= in_array($c['Field'],$auto) ? '(auto)' : fieldInput($c) ?></td>
<?php endforeach;?>
  <td>
    <input type="hidden" name="_act" value="add">
    <button class="btn">＋新增</button>
  </td>
</form>
</tr>


<?php foreach($rows as $r): ?>
<tr>
<form method="post">
  <?php foreach($cols as $c):
        $f=$c['Field'];
        echo '<td>'.fieldInput($c,$r[$f], $f==$pk||in_array($f,$auto)).'</td>';
      endforeach; ?>
  <td>
    <input type="hidden" name="_act" value="upd">
    <button class="btn">更新</button>
</form>
<form method="post" style="display:inline">
    <input type="hidden" name="_act" value="del">
    <input type="hidden" name="<?=$pk?>" value="<?=$r[$pk]?>">
    <button class="btn" onclick="return confirm('確定刪除?')">刪除</button>
</form>
  </td>
</tr>
<?php endforeach; ?>
</tbody>
</table>

</body></html>
