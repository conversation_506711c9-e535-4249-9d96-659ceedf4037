/*!40101 SET NAMES utf8mb4 */;

USE trialDB_01;

-- 建立員工資料表
CREATE TABLE EMPLOYEES (
    employee_id INT AUTO_INCREMENT PRIMARY KEY, -- 員工ID
    employee_number VARCHAR(50) UNIQUE NOT NULL, -- 員工編號
    full_name VARCHAR(255) NOT NULL,             -- 姓名
    job_title VARCHAR(100),                      -- 職稱
    department VARCHAR(100),                     -- 部門
    phone_number VARCHAR(50),                    -- 聯絡電話
    email VARCHAR(100),                          -- 電子郵件
    hire_date DATE,                              -- 入職日期
    notes TEXT,                                  -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入員工範例資料
INSERT INTO EMPLOYEES (employee_number, full_name, job_title, department, phone_number, email, hire_date) VALUES
('EMP001', '<PERSON>', '資深經理', '雲端應用研究二處研究部VII', '0912345678', '<PERSON><PERSON><PERSON>@example.com', '2012-06-04'),
('EMP002', '<PERSON><PERSON><PERSON>', '二級專員', '雲端應用研究二處研究部VII', '0923456789', '<EMAIL>', '2020-08-03'),
('EMP003', 'Charles Hsieh', '二級專員', '雲端應用研究二處研究部VII', '0934567890', '<EMAIL>', '2022-03-01'),
('EMP004', 'Jacky Tseng', '一級專員', '雲端應用研究二處研究部VII', '0923456789', '<EMAIL>', '2024-10-14'),
('EMP005', 'Jordan Chen', '二級專員', '雲端應用研究二處研究部VII', '0923456789', '<EMAIL>', '2022-01-03'),
('EMP006', 'Leo Haung', '二級專員', '雲端應用研究二處研究部VII', '0923456789', '<EMAIL>', '2020-09-07'),
('EMP007', 'Shaung Liao', '二級專員', '雲端應用研究二處研究部VII', '0923456789', '<EMAIL>', '2020-08-24'),
('EMP008', 'Jim Xiao', '工程師', '雲端應用研究二處研究部VII', '0923456789', '<EMAIL>', '2024-09-02')
;

-- 建立客戶資料表
CREATE TABLE CUSTOMERS (
    customer_id INT AUTO_INCREMENT PRIMARY KEY,    -- 客戶ID
    customer_name VARCHAR(255) NOT NULL,           -- 客戶名稱
    contact_person VARCHAR(100),                   -- 聯絡人
    phone_number VARCHAR(50),                      -- 聯絡電話
    email VARCHAR(100),                            -- 電子郵件
    address VARCHAR(255),                          -- 地址
    tax_id VARCHAR(50),                            -- 統一編號/稅號
    payment_terms TEXT,                            -- 付款條件
    notes TEXT,                                    -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入客戶範例資料
INSERT INTO CUSTOMERS (customer_name, contact_person, phone_number, email, address, tax_id, payment_terms) VALUES
('QCT Technology Co., Ltd.', 'Wang David', '03-12345678', '<EMAIL>', '桃園市龜山區文化二路2號', '12345678', 'None'),
('Tech Pioneer Co., Ltd.', 'Chen Michael', '02-12345678', '<EMAIL>', '台北市信義區忠孝東路一段1號', '87654321', 'Net 30'),
('Innovate Electronics Co., Ltd.', 'Lin Grace', '04-87654321', '<EMAIL>', '台中市西屯區台灣大道二段2號', '12345678', 'Net 60'),
('SmartLink Technology', 'Huang Andy', '07-23456789', '<EMAIL>', '高雄市前鎮區成功路3號', '56789012', 'T/T in Advance');

-- 建立供應商資料表
CREATE TABLE SUPPLIERS (
    supplier_id INT AUTO_INCREMENT PRIMARY KEY,    -- 供應商ID
    supplier_name VARCHAR(255) NOT NULL,           -- 供應商名稱
    contact_person VARCHAR(100),                   -- 聯絡人
    phone_number VARCHAR(50),                      -- 聯絡電話
    email VARCHAR(100),                            -- 電子郵件
    address VARCHAR(255),                          -- 地址
    tax_id VARCHAR(50),                            -- 統一編號/稅號
    notes TEXT,                                    -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入供應商範例資料
INSERT INTO SUPPLIERS (supplier_name, contact_person, phone_number, email, address, tax_id) VALUES
('DeLite Energy Technology Co., Ltd.', 'Wang David', '03-11112222', '<EMAIL>', '桃園市龜山區文化一路1號', '98765432'),
('Cotech Electronics Co., Ltd.', 'Lee Lisa', '03-33334444', '<EMAIL>', '新北市樹林區中華路2號', '21098765'),
('BroMicom Technology Co., Ltd.', 'Chao Mary', '06-55556666', '<EMAIL>', '台南市安平區工業區3號', '67890123'),
('AVATA Technology Co., Ltd.', 'Chen Amy', '07-77778888', '<EMAIL>', '高雄市前鎮區成功路4號', '12345678'),
('Kernesis Technology Co., Ltd.', 'Lin Kevin', '02-99990000', '<EMAIL>', '台北市內湖區瑞光路5號', '23456789'),
('LogiData Technology Co., Ltd.', 'Huang Grace', '04-22223333', '<EMAIL>', '台中市南屯區工業路6號', '34567890'),
('HexaCore Technology Co., Ltd.', 'Chang Eric', '02-44445555', '<EMAIL>', '新北市三重區中正路7號', '45678901'),
('Develon Technology Co., Ltd.', 'Liu Jenny', '03-66667777', '<EMAIL>', '桃園市蘆竹區中正路8號', '56789012'),
('MetaMind Technology Co., Ltd.', 'Chen Michael', '02-88889999', '<EMAIL>', '新北市板橋區文化路9號', '12345678'),
('Dyneon Technology Co., Ltd.', 'Cheng Andy', '06-88889999', '<EMAIL>', '台南市新營區工業路10號', '67890123'),
('FrostyCool Materials Co., Ltd.', 'Cheng Emily', '07-00001111', '<EMAIL>', '高雄市苓雅區中華路11號', '78901234');


-- 建立工廠資料表
CREATE TABLE FACTORIES (
    factory_id INT AUTO_INCREMENT PRIMARY KEY,     -- 工廠ID
    factory_name VARCHAR(255) NOT NULL,            -- 工廠名稱
    location VARCHAR(255),                         -- 工廠地址
    contact_person VARCHAR(100),                   -- 聯絡人
    phone_number VARCHAR(50),                      -- 聯絡電話
    notes TEXT,                                    -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入工廠範例資料
INSERT INTO FACTORIES (factory_name, location, contact_person, phone_number) VALUES
('Factory A', '桃園市龜山區文化二路211號', 'Aaron Lin', '03-88887777'),
('Factory B', '台南市新市區南科一路2號', 'Miller Haung', '06-99998888'),
('Factory C', '高雄市路竹區環球路3號', 'Kate Wu', '07-12123434');

-- 建立產品資料表
CREATE TABLE PRODUCTS (
    product_id INT AUTO_INCREMENT PRIMARY KEY,     -- 產品ID
    product_code VARCHAR(50) UNIQUE NOT NULL,      -- 產品編號
    product_name VARCHAR(255) NOT NULL,            -- 產品名稱
    product_model VARCHAR(100),                    -- 產品型號
    description TEXT,                              -- 產品描述
    -- unit_price DECIMAL(10,2) NOT NULL,            -- 單價
    lead_time_days INT,                            -- 標準生產交期 (天)
    file_path VARCHAR(255),                        -- 設計檔案路徑
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入產品範例資料
INSERT INTO PRODUCTS (product_code, product_name, product_model, description, lead_time_days, file_path) VALUES
('1S7RF9Z0001', 'G7B', 'Golden_1', 'G7BC Series AI Server', 14, '/designs/S7B/golden_1'),
('1S7RF9Z0002', 'G7B', 'Golden_2', 'G7BC Series AI Server', 14, '/designs/S7B/golden_2'),
('1S7RF9Z0003', 'G7B', 'Golden_3', 'G7BC Series AI Server', 14, '/designs/S7B/golden_3'),
('1HY7UZZ07Q', 'G8D', 'LGD_1', 'G8DC Series AI Server', 15, '/designs/S8D/lgd_1'),
('1HY7UZZ07R', 'G8D', 'LGD_2', 'G8DC Series AI Server', 15, '/designs/S8D/lgd_2');

-- 建立物料資料表
CREATE TABLE MATERIALS (
    material_id INT AUTO_INCREMENT PRIMARY KEY,    -- 物料ID
    material_code VARCHAR(50) UNIQUE NOT NULL,     -- 料號
    material_name VARCHAR(255) NOT NULL,           -- 物料名稱
    specification VARCHAR(255),                    -- 規格
    unit_of_measure VARCHAR(20) NOT NULL,          -- 計量單位
    category VARCHAR(50),                          -- 物料類別
    current_stock DECIMAL(10,2) DEFAULT 0,         -- 目前庫存量
    min_stock_level DECIMAL(10,2) DEFAULT 0,       -- 最低庫存量警告
    max_stock_level DECIMAL(10,2) DEFAULT 0,       -- 最高庫存量
    supplier_id INT,                               -- 供應商ID
    unit_cost DECIMAL(10,2),                       -- 單價
    location VARCHAR(100),                         -- 存放位置
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(supplier_id)
);


-- 插入物料範例資料
INSERT INTO MATERIALS (material_code, material_name, specification, unit_of_measure, category, current_stock, min_stock_level, max_stock_level, supplier_id, unit_cost, location) VALUES
('DD0S7BPB100', 'DC Power Cable 100A', 'DC Power Module 100A, 18AWG, 100cm', 'PCS', 'ELEC', 150.00, 20.00, 300.00, 1, 500.00, 'D3-02'),
('DD0S7BPB101', 'DC Power Cable 200A', 'DC Power Module 200A, 16AWG, 200cm', 'PCS', 'ELEC', 100.00, 15.00, 250.00, 1, 600.00, 'D3-03'),
('DGRA00748', 'CPU Clip E1B', 'E1B Intel LGA115x HCS', 'PCS', 'MECH', 300.00, 50.00, 600.00, 2, 15.00, 'E4-04'),
('DGRB00749', 'CPU Clip E1C', 'E1C AMD AM4 DEP', 'PCS', 'MECH', 200.00, 30.00, 400.00, 2, 20.00, 'E4-05'),
('FBS7B00610', 'Heat Sink H001B', 'H001B Aluminum Fin', 'PCS', 'COOL', 100.00, 10.00, 200.00, 3, 50.00, 'F5-01'),
('FBS8D00611', 'Heat Sink L020B', 'L020B Copper Heatpipe', 'PCS', 'COOL', 80.00, 5.00, 150.00, 3, 55.00, 'F5-02'),
('ATR6G00801', 'AVATA DDR5 16GB', 'DDR5 16GB 4800MHz ECC', 'PCS', 'RAM', 150.00, 25.00, 300.00, 4, 180.00, 'G6-01'),
('ATR6G00802', 'AVATA DDR5 32GB', 'DDR5 32GB 4800MHz ECC', 'PCS', 'RAM', 100.00, 15.00, 250.00, 4, 360.00, 'G6-02'),
('ATR6G00712', 'KCS DDR5 16GB', 'DDR5 16GB 4800MHz ECC', 'PCS', 'RAM', 120.00, 20.00, 250.00, 5, 200.00, 'G6-01'),
('ATR6G00713', 'KCS DDR5 32GB', 'DDR5 32GB 4800MHz ECC', 'PCS', 'RAM', 80.00, 10.00, 200.00, 5, 400.00, 'G6-02'),
('ABS400FM001', 'AVATA M.2 SSD NVMe 1TB', 'M.2 SSD 2280 NVMe 1TB Gen4x4', 'PCS', 'STOR', 200.00, 30.00, 500.00, 4, 1500.00, 'H7-01'),
('ABS400FM002', 'AVATA M.2 SSD NVMe 2TB', 'M.2 SSD 2280 NVMe 2TB Gen4x4', 'PCS', 'STOR', 150.00, 20.00, 400.00, 4, 2500.00, 'H7-02'),
('ABS400FK001', 'KCS M.2 SSD NVMe 1TB', 'Write 8700 Read 10000', 'PCS', 'STOR', 180.00, 25.00, 450.00, 5, 1200.00, 'H7-03'),
('ABS400FK002', 'KCS M.2 SSD NVMe 2TB', 'Write 8500 Read 9500', 'PCS', 'STOR', 120.00, 15.00, 300.00, 5, 2000.00, 'H7-04'),
('FBSSB22202', 'Dummy Board FHHL', 'Dummy FHHL Aluminum Alloy', 'PCS', 'BRKT', 300.00, 50.00, 600.00, 2, 100.00, 'I8-01'),
('FBSSB22203', 'Dummy Board FHFL', 'Dummy FHFL Aluminum Alloy', 'PCS', 'BRKT', 250.00, 40.00, 500.00, 2, 120.00, 'I8-02'),
('MS300PC801', 'Power Supply 800W', '800W, 80 PLUS Gold', 'PCS', 'PSU', 100.00, 20.00, 300.00, 1, 3000.00, 'J9-01'),
('MS300PC802', 'Power Supply 1200W', '1200W, 80 PLUS Platinum', 'PCS', 'PSU', 80.00, 15.00, 250.00, 1, 4000.00, 'J9-02'),
('EB4100C01', 'Ethernet Switch 24-Port', '24-Port RJ45', 'PCS', 'NET', 60.00, 10.00, 150.00, 6, 5000.00, 'K10-01'),
('EB4100C02', 'Ethernet Switch 48-Port', '48-Port RJ45', 'PCS', 'NET', 40.00, 5.00, 100.00, 6, 8000.00, 'K10-02'),
('HCS500D001', 'CPU HCS500 16-Core', '16C/32T, 2.5GHz Base', 'PCS', 'CPU', 120.00, 20.00, 250.00, 7, 6000.00, 'L11-01'),
('HCS500D002', 'CPU HCS500 32-Core', '32C/64T, 3.0GHz Base', 'PCS', 'CPU', 80.00, 10.00, 200.00, 7, 10000.00, 'L11-02'),
('DEP2004IC002', 'CPU DEP9005 160-Core', '160C/320T 2.35GHz 390W', 'PCS', 'CPU', 150.00, 20.00, 350.00, 8, 8000.00, 'M12-01'),
('DEP2004IC001', 'CPU DEP9005 192-Core', '192C/384T 3.7GHz 500W', 'PCS', 'CPU', 200.00, 30.00, 400.00, 8, 5000.00, 'M12-02'),
('MM2004IC001', 'GPU MM2004 80GB', '80GB GDDR6, PCIe Gen4x16', 'PCS', 'GPU', 150.00, 25.00, 300.00, 9, 7000.00, 'M12-01'),
('MM2004IC002', 'GPU MM2004 120GB', '120GB GDDR6, PCIe Gen4x16', 'PCS', 'GPU', 100.00, 15.00, 250.00, 9, 12000.00, 'M12-02'),
('DIN35MI001', 'GPU DIN 64GB', '64GB HBM2 300W, PCIe4x16', 'PCS', 'GPU', 80.00, 10.00, 200.00, 10, 9000.00, 'N13-01'),
('DIN35MI002', 'GPU DIN 128GB', '128GB HBM2 400W, PCIe4x16', 'PCS', 'GPU', 60.00, 5.00, 150.00, 10, 15000.00, 'N13-02'),
('DD0S8D001', 'Screw M3x10mm', 'M3x10mm, 10mm Length, Phillips, Stainless', 'PCS', 'SCREW', 500.00, 100.00, 1000.00, 11, 0.10, 'N13-01'),
('DD0S8D002', 'Screw M4x20mm', 'M4x20mm, 20mm Length, Phillips, Stainless', 'PCS', 'SCREW', 300.00, 50.00, 600.00, 11, 0.15, 'N13-02'),
('DD0FJ001', 'Nut M3', 'M3, Hex Nut, Stainless', 'PCS', 'NUT', 400.00, 80.00, 800.00, 11, 0.05, 'N13-03'),
('DD0FJ002', 'Nut M4', 'M4, Hex Nut, Stainless', 'PCS', 'NUT', 250.00, 40.00, 500.00, 11, 0.08, 'N13-04'),
('DD0HH001', 'Washer M3', 'M3, Stainless', 'PCS', 'WSHR', 600.00, 120.00, 1200.00, 11, 0.02, 'N13-05'),
('DD0HH002', 'Washer M4', 'M4, Stainless', 'PCS', 'WSHR', 350.00, 70.00, 700.00, 11, 0.03, 'N13-06');

-- 建立物料清單資料表
CREATE TABLE BILLS_OF_MATERIALS (
    bom_id INT AUTO_INCREMENT PRIMARY KEY,         -- BOM ID
    product_id INT NOT NULL,                       -- 產品識別碼
    material_id INT NOT NULL,                      -- 物料識別碼
    quantity DECIMAL(10,2) NOT NULL,               -- 所需物料數量
    notes VARCHAR(255),                            -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES PRODUCTS(product_id),
    FOREIGN KEY (material_id) REFERENCES MATERIALS(material_id)
);

-- 插入物料清單範例資料
INSERT INTO BILLS_OF_MATERIALS (product_id, material_id, quantity) VALUES
(1, 1, 2.00), -- DC Power Cable 100A for G7B_Golden_1
(1, 3, 2.00), -- CPU Clip E1B HCS for G7B_Golden_1
(1, 5, 2.00), -- Heat Sink H001B for G7B_Golden_1
(1, 7, 8.00), -- AVATA DDR5 16GB for G7B_Golden_1
(1, 11, 2.00), -- AVATA M.2 SSD NVMe 1TB for G7B_Golden_1
(1, 15, 1.00), -- Dummy Board FHHL for G7B_Golden_1
(1, 17, 2.00), -- Power Supply 800W for G7B_Golden_1
(1, 19, 1.00), -- Ethernet Switch 24-Port for G7B_Golden_1
(1, 21, 2.00), -- HCS500 CPU 16-Core for G7B_Golden_1
(1, 25, 4.00), -- MM2004 GPU 80GB for G7B_Golden_1
(1, 29, 12.00), -- Screw M3 for G7B_Golden_1
(1, 31, 12.00), -- Nut M3 for G7B_Golden_1
(1, 33, 12.00), -- Washer M3 for G7B_Golden_1
(2, 1, 2.00), -- DC Power Cable 100A for G7B_Golden_2
(2, 3, 1.00), -- CPU Clip E1B HCS for G7B_Golden_2
(2, 5, 2.00), -- Heat Sink H001B for G7B_Golden_2
(2, 9, 8.00), -- KCS DDR5 16GB for G7B_Golden_2
(2, 13, 2.00), -- KCS M.2 SSD NVMe 1TB for G7B_Golden_2
(2, 15, 1.00), -- Dummy Board FHHL for G7B_Golden_2
(2, 17, 2.00), -- Power Supply 800W for G7B_Golden_2
(2, 19, 1.00), -- Ethernet Switch 24-Port for G7B_Golden_2
(2, 21, 2.00), -- HCS500 CPU 16-Core for G7B_Golden_2
(2, 25, 4.00), -- MM2004 GPU 80GB for G7B_Golden_2
(2, 29, 12.00), -- Screw M3 for G7B_Golden_2
(2, 31, 12.00), -- Nut M3 for G7B_Golden_2
(2, 33, 12.00), -- Washer M3 for G7B_Golden_2
(3, 2, 2.00), -- DC Power Cable 200A for G7B_Golden_3
(3, 3, 1.00), -- CPU Clip E1B HCS for G7B_Golden_3
(3, 5, 2.00), -- Heat Sink H001B for G7B_Golden_3
(3, 8, 8.00), -- AVATA DDR5 32GB for G7B_Golden_3
(3, 12, 2.00), -- AVATA M.2 SSD NVMe 2TB for G7B_Golden_3
(3, 15, 1.00), -- Dummy Board FHHL for G7B_Golden_3
(3, 18, 2.00), -- Power Supply 1200W for G7B_Golden_3
(3, 19, 1.00), -- Ethernet Switch 24-Port for G7B_Golden_3
(3, 22, 2.00), -- HCS500 CPU 32-Core for G7B_Golden_3
(3, 26, 4.00), -- MM2004 GPU 120GB for G7B_Golden_3
(3, 29, 12.00), -- Screw M3 for G7B_Golden_3
(3, 31, 12.00), -- Nut M3 for G7B_Golden_3
(3, 33, 12.00), -- Washer M3 for G7B_Golden_3
(4, 1, 2.00), -- DC Power Cable 100A for G8D_LGD_1
(4, 4, 1.00), -- CPU Clip E1C AMD for G8D_LGD_1
(4, 6, 2.00), -- Heat Sink L020B for G8D_LGD_1
(4, 7, 8.00), -- AVATA DDR5 16GB for G8D_LGD_1
(4, 11, 2.00), -- AVATA M.2 SSD NVMe 1TB for G8D_LGD_1
(4, 15, 1.00), -- Dummy Board FHHL for G8D_LGD_1
(4, 17, 2.00), -- Power Supply 800W for G8D_LGD_1
(4, 19, 1.00), -- Ethernet Switch 24-Port for G8D_LGD_1
(4, 23, 2.00), -- DEP9005 CPU 160-Core for G8D_LGD_1
(4, 27, 4.00), -- DIN GPU 64GB for G8D_LGD_1
(4, 30, 12.00), -- Screw M4 for G8D_LGD_1
(4, 32, 12.00), -- Nut M4 for G8D_LGD_1
(4, 34, 12.00), -- Washer M4 for G8D_LGD_1
(5, 2, 1.00), -- DC Power Cable 200A for G8D_LGD_2
(5, 4, 1.00), -- CPU Clip E1C AMD for G8D_LGD_2
(5, 6, 2.00), -- Heat Sink L020B for G8D_LGD_2
(5, 8, 8.00), -- AVATA DDR5 32GB for G8D_LGD_2
(5, 12, 2.00), -- AVATA M.2 SSD NVMe 2TB for G8D_LGD_2
(5, 15, 1.00), -- Dummy Board FHHL for G8D_LGD_2
(5, 18, 2.00), -- Power Supply 1200W for G8D_LGD_2
(5, 20, 1.00), -- Ethernet Switch 48-Port for G8D_LGD_2
(5, 24, 2.00), -- DEP9005 CPU 192-Core for G8D_LGD_2
(5, 28, 4.00), -- DIN GPU 128GB for G8D_LGD_2
(5, 30, 12.00), -- Screw M4 for G8D_LGD_2
(5, 32, 12.00), -- Nut M4 for G8D_LGD_2
(5, 34, 12.00); -- Washer M4 for G8D_LGD_2

-- 建立客戶訂單資料表
CREATE TABLE CUSTOMER_ORDERS (
    order_id INT AUTO_INCREMENT PRIMARY KEY,       -- 訂單ID
    order_number VARCHAR(50) UNIQUE NOT NULL,      -- 訂單編號
    customer_id INT NOT NULL,                      -- 客戶ID
    order_date DATE NOT NULL,                      -- 訂單日期
    required_delivery_date DATE,                   -- 要求交期
    order_status VARCHAR(50),                      -- 訂單狀態
    shipping_address VARCHAR(255),                 -- 出貨地址
    notes TEXT,                                    -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES CUSTOMERS(customer_id)
);

-- 插入客戶訂單範例資料
INSERT INTO CUSTOMER_ORDERS (order_number, customer_id, order_date, required_delivery_date, order_status, shipping_address) VALUES
('CUSTORD-202506001', 2, '2025-06-10', '2025-07-25', 'Pending', '台北市信義區忠孝東路一段1號'),
('CUSTORD-202506002', 3, '2025-06-12', '2025-08-15', 'Processing', '台中市西屯區台灣大道二段2號'),
('CUSTORD-202506003', 2, '2025-06-13', '2025-08-05', 'Pending', '台北市信義區忠孝東路一段1號');


-- 建立客戶訂單明細資料表
CREATE TABLE CUSTOMER_ORDER_DETAIL (
    order_detail_id INT AUTO_INCREMENT PRIMARY KEY, -- 訂單明細ID
    order_id INT NOT NULL,                          -- 訂單ID
    product_id INT NOT NULL,                        -- 產品ID
    quantity INT NOT NULL,                          -- 訂購數量
    -- unit_price DECIMAL(10,2) NOT NULL,           -- 當時訂購的單價
    -- subtotal DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED, -- 小計 (數量 * 單價)
    notes VARCHAR(255),                             -- 備註
    FOREIGN KEY (order_id) REFERENCES CUSTOMER_ORDERS(order_id),
    FOREIGN KEY (product_id) REFERENCES PRODUCTS(product_id)
);

-- 插入客戶訂單明細範例資料
INSERT INTO CUSTOMER_ORDER_DETAIL (order_id, product_id, quantity) VALUES
(1, 1, 1000),
(2, 4, 500),
(3, 1, 500);

-- 建立採購申請資料表
CREATE TABLE PURCHASE_REQUEST (
    request_id INT AUTO_INCREMENT PRIMARY KEY,     -- 採購申請ID
    request_number VARCHAR(50) UNIQUE NOT NULL,    -- 申請單號
    request_date DATE NOT NULL,                    -- 申請日期
    department VARCHAR(100),                       -- 申請部門
    request_user_id INT,                           -- 申請人ID
    material_id INT NOT NULL,                      -- 物料ID
    quantity DECIMAL(10,2) NOT NULL,               -- 申請數量
    unit_of_measure VARCHAR(20),                   -- 計量單位
    required_date DATE,                            -- 需求日期
    notes TEXT,                                    -- 備註
    request_status VARCHAR(50),                    -- 申請狀態
    approval_user_id INT,                          -- 審核人ID
    approval_date DATETIME,                        -- 審核日期時間
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_user_id) REFERENCES EMPLOYEES(employee_id),
    FOREIGN KEY (approval_user_id) REFERENCES EMPLOYEES(employee_id),
    FOREIGN KEY (material_id) REFERENCES MATERIALS(material_id)
);

-- 插入採購申請範例資料
INSERT INTO PURCHASE_REQUEST (request_number, request_date, department, request_user_id, material_id, quantity, unit_of_measure, required_date, request_status, approval_user_id, approval_date) VALUES
('PR-202506001', '2025-06-10', 'Production Dept.', 2, 1, 50.00, 'PCS', '2025-06-20', 'Approved', 1, '2025-06-11 10:00:00'),
('PR-202506002', '2025-06-11', 'Production Dept.', 2, 13, 30.00, 'PCS', '2025-06-25', 'Pending', NULL, NULL),
('PR-202506003', '2025-06-12', 'Production Dept.', 2, 5, 5.00, 'PCS', '2025-07-01', 'Rejected', 1, '2025-06-12 14:30:00'),
('PR-202506004', '2025-06-13', 'Production Dept.', 2, 7, 10.00, 'PCS', '2025-07-05', 'Approved', 1, '2025-06-14 09:00:00'),
('PR-202506005', '2025-06-14', 'Production Dept.', 2, 7, 20.00, 'PCS', '2025-07-10', 'Pending', NULL, NULL),
('PR-202506006', '2025-06-15', 'Production Dept.', 2, 17, 15.00, 'PCS', '2025-07-15', 'Approved', 1, '2025-06-16 11:00:00');

-- 建立採購單資料表
CREATE TABLE PURCHASE_ORDERS (
    order_id INT AUTO_INCREMENT PRIMARY KEY,       -- 採購單ID
    order_number VARCHAR(50) UNIQUE NOT NULL,      -- 採購單號
    supplier_id INT NOT NULL,                      -- 供應商ID
    order_date DATE NOT NULL,                      -- 下單日期
    expected_delivery_date DATE,                   -- 預計交貨日期
    total_amount DECIMAL(10,2),                    -- 訂單總金額
    order_status VARCHAR(50),                      -- 訂單狀態
    payment_terms VARCHAR(100),                    -- 付款條件
    shipping_address VARCHAR(255),                 -- 收貨地址
    notes TEXT,                                    -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(supplier_id)
);

-- 插入採購單範例資料
INSERT INTO PURCHASE_ORDERS (order_number, supplier_id, order_date, expected_delivery_date, total_amount, order_status, payment_terms, shipping_address) VALUES
('PO-202506001', 1, '2025-06-11', '2025-06-20', 6000.00, 'Comfirmed', 'Net 30', '桃園市龜山區文化一路'),
('PO-202506003', 4, '2025-06-13', '2025-07-01', 5000.00, 'Pending', 'T/T in Advance', '桃園市龜山區文化一路');

-- 建立採購單明細資料表
CREATE TABLE PURCHASE_ORDER_DETAIL (
    order_detail_id INT AUTO_INCREMENT PRIMARY KEY, -- 採購單明細ID
    order_id INT NOT NULL,                          -- 採購單ID
    request_id INT NOT NULL,                       -- 採購申請ID
    material_id INT NOT NULL,                       -- 物料ID
    quantity DECIMAL(10,2) NOT NULL,                -- 採購數量
    -- unit_price DECIMAL(10,2) NOT NULL,              -- 單價
    -- subtotal DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED, -- 小計 (數量 * 單價)
    notes VARCHAR(255),                             -- 備註
    FOREIGN KEY (order_id) REFERENCES PURCHASE_ORDERS(order_id),
    FOREIGN KEY (request_id) REFERENCES PURCHASE_REQUEST(request_id),
    FOREIGN KEY (material_id) REFERENCES MATERIALS(material_id)
);

-- 插入採購單明細範例資料
INSERT INTO PURCHASE_ORDER_DETAIL (order_id, request_id, material_id, quantity) VALUES
(1, 1, 1, 50.00), -- PO-202506001
(1, 6, 17, 15.00); -- PO-202506001

-- 建立收貨資料表
CREATE TABLE GOODS_RECEIPT (
    receipt_id INT AUTO_INCREMENT PRIMARY KEY,     -- 收貨記錄ID
    order_id INT NOT NULL,                         -- 採購單ID
    receipt_date DATETIME DEFAULT CURRENT_TIMESTAMP, -- 收貨日期時間
    received_by_user_id INT,                       -- 收貨人ID
    notes TEXT,                                    -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES PURCHASE_ORDERS(order_id),
    FOREIGN KEY (received_by_user_id) REFERENCES EMPLOYEES(employee_id)
);
-- 插入收貨範例資料
INSERT INTO GOODS_RECEIPT (order_id, received_by_user_id, notes) VALUES
(1, 1, 'DC Power Cable received and stocked'), -- Partial receipt for Purchase Order 1
(1, 1, 'Power Supply received'); -- Another receipt for Purchase Order 1

-- 建立收貨明細資料表 (GOODS_RECEIPT_DETAIL)
CREATE TABLE GOODS_RECEIPT_DETAIL (
    receipt_detail_id INT AUTO_INCREMENT PRIMARY KEY, -- 收貨明細ID
    receipt_id INT NOT NULL,                          -- 收貨記錄ID
    material_id INT NOT NULL,                         -- 物料ID
    received_quantity DECIMAL(10,2) NOT NULL,         -- 實收數量
    unit_of_measure VARCHAR(20),                      -- 計量單位
    notes VARCHAR(255),                               -- 備註
    FOREIGN KEY (receipt_id) REFERENCES GOODS_RECEIPT(receipt_id),
    FOREIGN KEY (material_id) REFERENCES MATERIALS(material_id)
);
-- 插入收貨明細範例資料 (這裡假設收貨後會更新MATERIALS.current_stock，但這裡僅插入數據)
INSERT INTO GOODS_RECEIPT_DETAIL (receipt_id, material_id, received_quantity, unit_of_measure) VALUES
(1, 1, 50.00, 'PCS'),  -- 收貨記錄1
(2, 17, 15.00, 'PCS');  -- 收貨記錄2

-- 建立工單資料表 (WORK_ORDERS)
CREATE TABLE WORK_ORDERS (
    work_order_id INT AUTO_INCREMENT PRIMARY KEY,    -- 工單ID
    work_order_number VARCHAR(50) UNIQUE NOT NULL,   -- 工單編號
    order_id INT,                                    -- 相關客戶訂單ID
    product_id INT NOT NULL,                         -- 生產產品ID
    quantity INT NOT NULL,                           -- 計劃生產數量
    start_date DATE,                                 -- 計劃開始日期
    end_date DATE,                                   -- 計劃結束日期
    actual_start_date DATETIME,                      -- 實際開始日期時間
    actual_end_date DATETIME,                        -- 實際結束日期時間
    production_status VARCHAR(50),                   -- 生產狀態
    factory_id INT,                                  -- 生產工廠ID
    priority INT,                                    -- 優先度 (1-高, 2-中, 3-低)
    notes TEXT,                                      -- 備註
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES CUSTOMER_ORDERS(order_id),
    FOREIGN KEY (product_id) REFERENCES PRODUCTS(product_id),
    FOREIGN KEY (factory_id) REFERENCES FACTORIES(factory_id)
);
-- 插入工單範例資料
INSERT INTO WORK_ORDERS (work_order_number, order_id, product_id, quantity, start_date, end_date, production_status, factory_id, priority) VALUES
('WO-202506001', 1, 1, 1000, '2025-06-20', '2025-06-27', 'Processing', 1, 1), -- Product 1 for Order 1
('WO-202506002', 2, 4, 500, '2025-06-25', '2025-07-08', 'Pending', 1, 2),  -- Product 4 for Order 2
('WO-202506003', 3, 1, 500, '2025-06-30', '2025-07-15', 'Pending', 1, 1); -- Product 1 for Order 3


-- 建立工單物料需求與領用資料表 (WORK_ORDER_MATERIAL)
CREATE TABLE WORK_ORDER_MATERIAL (
    wo_material_id INT AUTO_INCREMENT PRIMARY KEY, -- 工單物料ID
    work_order_id INT NOT NULL,                    -- 工單ID
    material_id INT NOT NULL,                      -- 物料ID
    required_quantity DECIMAL(10,2) NOT NULL,      -- 計劃所需數量
    issued_quantity DECIMAL(10,2) DEFAULT 0,       -- 已領用數量
    issue_date DATETIME,                           -- 領用日期時間
    notes VARCHAR(255),                            -- 備註
    FOREIGN KEY (work_order_id) REFERENCES WORK_ORDERS(work_order_id),
    FOREIGN KEY (material_id) REFERENCES MATERIALS(material_id)
);
-- 插入工單物料範例資料
INSERT INTO WORK_ORDER_MATERIAL (work_order_id, material_id, required_quantity, issued_quantity, issue_date) VALUES
(1, 1, 4, 4, '2025-06-20 08:00:00'), -- 領用4個DC Power Cable 100A for Work Order 1
(1, 3, 2, 2, '2025-06-20 08:00:00'), -- 領用2個CPU Clip E1B HCS for Work Order 1
(1, 5, 2, 2, '2025-06-20 08:00:00'), -- 領用2個Heat Sink H001B for Work Order 1
(1, 7, 8, 8, '2025-06-20 08:00:00'), -- 領用8個AVATA DDR5 16GB for Work Order 1
(1, 11, 2, 2, '2025-06-20 08:00:00'), -- 領用2個AVATA M.2 SSD NVMe 1TB for Work Order 1
(1, 15, 1, 1, '2025-06-20 08:00:00'), -- 領用1個Dummy Board FHHL for Work Order 1
(1, 17, 2, 2, '2025-06-20 08:00:00'), -- 領用2個Power Supply 800W for Work Order 1
(1, 19, 1, NULL, NULL), -- 未領用Ethernet Switch for Work Order 1
(1, 21, 2, NULL, NULL), -- 未領用HCS500 CPU for Work Order 1
(1, 25, 4, NULL, NULL), -- 未領用MM2004 GPU for Work Order 1
(1, 29, 12, NULL, NULL), -- 未領用Screw M3 for Work Order 1
(1, 31, 12, NULL, NULL), -- 未領用Nut M3 for Work Order 1
(1, 33, 12, NULL, NULL); -- 未領用Washer M3 for Work Order 1
