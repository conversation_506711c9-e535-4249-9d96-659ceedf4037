## 目錄架構
```plaintext
MySQL/
├── server.py               # Implementation for SSE transport
├── client.py               # User chat with agent here
├── interfaces/             # Base classes/interfaces for tools and resources
│   └── tool.py
├── services/               # Connect to local MySQL
│   └── mysql_connection.py
├── tools/
│   ├── mysql_agents.py     # Tool that executes SQL queries on a MySQL database.(Currently)
│   ├── crud.py             # DB CRUD operations
│   ├── describe_tables.py  # Show table schema/details
│   ├── list_tables.py      # List all tables
│   └── only_select.py      # Only select (read-only)
├── resources/
├── simulated_data/
	├── db      			# Simulated data provided by <PERSON>
	├── web      			# A simple web page for interacting with MySQL
	└── docker-compose.yml	# To establish a simple web page services
├── pictures/
│   ├── io_schema.png       # Show the input/output schema
│   ├── Q1.png              # Please find the job title of <PERSON>.
│   ├── Q2.png              # Please find the shipping address of order number CUSTORD-202506001.
│   ├── Q3.png              # How many material DGRA00748 remaining?
│   └── Q4.png              # What's the supplier's name of material DGRA00748?
├── .python-version
├── pyproject.toml
└── README.md
```

## Schema
![io_schema](pictures/io_schema.png)  

## MySQL
There is a simple for easy view MySQL.  
<http://************:8080>
![mysql_material](pictures/mysql_material.png)

### To establish a simple web page for interacting with MySQL.  
> Use utf8mb4 for the MySQL connection and table schemas to ensure Traditional Chinese displays correctly:  
/*!40101 SET NAMES utf8mb4 */; and CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci.

```code
cd MySQL/simulated_data
```

```code
docker compose up
```

## Quick Setup

### Use uv to setup environment
After install uv
```bash
uv venv .venv
source .venv/bin/activate
uv pip sync
```

### Start  Server

```bash
uv run server.py
```

### Start Client

```bash
uv run client.py
```

### 開始問問題

- 啟動 `client.py` 後，即可直接在 terminal 輸入自然語言問問題.  
---

## Results  
1. Scenario about lack of material.
	```code
	For the new order, we need 400 units of material DGRA00748.
	```
	Explain:  
	1. User submits a new order or inquires about potential shortages.
	2. The agent checks the current inventory of the requested material in the database.
	3. If a shortage is detected (current stock is less than the required quantity), the agent gathers all relevant details, including material name, material code, current stock, and required quantity.
	4. The agent sends an email notification to the responsible employee. If the responsible person is unknown, the notification is sent to Jim Xiao by default.
	  
	![demo_shorts](pictures/demo_shorts.png) 

2. Another QA:  
	Q1 :
	```code
	Please find the job title of Jim Xiao.
	```
	![Q1](pictures/Q1.png)

	Q2 :
	```code
	Please find the shipping address of order number CUSTORD-202506001.
	```
	![Q2](pictures/Q2.png)  

	Q3 :
	```code
	How many material DGRA00748 remaining?
	```
	![Q3](pictures/Q3.png)  

	Q4 :
	```code
	What's the supplier's name of material DGRA00748?
	```
	![Q4](pictures/Q4.png)  


