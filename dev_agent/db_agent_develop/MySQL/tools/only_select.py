from interfaces.tool import BaseToolInput, BaseToolOutput, Tool
from services.mysql_connection import MySQL_Service
from pydantic import Field

class OnlySelectInput(BaseToolInput):
    """
    Input schema for the OnlySelectTool.

    Attributes:
        query (str): The SELECT SQL query to be executed. Must start with 'SELECT'.
    """
    query: str = Field(
        ...,
        description=(
            "The input MUST be a valid SQL SELECT statement, as a string. "
            "For example: 'SELECT job_title FROM EMPLOYEES WHERE full_name = \"Jim <PERSON>\"'"
        )
    )

class OnlySelectOutput(BaseToolOutput):
    """
    Output schema for the OnlySelectTool.

    Attributes:
        rows (list[dict]): Result rows of the SELECT query.
    """
    rows: list[dict] = Field(..., description="Result rows of the SELECT query.")

class OnlySelectTool(Tool):
    """
    Tool for executing SQL SELECT statements (read-only).
    
    Description:
        This tool executes a SQL SELECT statement.
        The input MUST be a dictionary with a single key 'query', whose value is a valid SQL SELECT statement as a string.
        Do NOT use 'table', 'columns', 'where', 'conditions', or any other keys.
        For example: {'query': 'SELECT job_title FROM EMPLOYEES WHERE full_name = \"Jim Xiao\"'}
    """
    name = "only_select"
    description = (
        "Execute a SQL SELECT query on the database. "
        "The input MUST be a dictionary with a single key 'query', whose value is a valid SQL SELECT statement as a string. "
        "Do NOT provide any other keys such as 'table', 'columns', or 'conditions'. "
        "Example: {'query': 'SELECT job_title FROM EMPLOYEES WHERE full_name = \"Jim Xiao\"'}"
    )
    input_schema = OnlySelectInput
    output_schema = OnlySelectOutput

    def execute(self, params: OnlySelectInput | dict, **kwargs) -> OnlySelectOutput:
        """
        Execute a SELECT SQL query.

        Args:
            params (OnlySelectInput or dict): The input containing the SELECT query.
            **kwargs: Additional keyword arguments (not used).

        Returns:
            OnlySelectOutput: The output containing the query result rows.

        Raises:
            ValueError: If the query does not start with 'SELECT'.
        """
        # Accept both OnlySelectInput and dict for robustness
        if isinstance(params, dict):
            # Hotfix: If LLM still outputs table/columns/conditions, auto-compose query
            if "query" not in params and all(k in params for k in ("table", "columns", "conditions")):
                query = f"SELECT {', '.join(params['columns'])} FROM {params['table']} WHERE {' AND '.join(params['conditions'])}"
                params = {"query": query}
            elif "query" not in params:
                raise ValueError("Missing 'query' key in input. The input must be: {'query': '<SQL SELECT statement>'}")

            query = params["query"]
        else:
            query = params.query

        # Only allow SELECT statements
        if not query.strip().lower().startswith("select"):
            raise ValueError("Only SELECT statements are allowed.")

        # Connect to the database and execute the query
        with MySQL_Service().get_conn() as conn, conn.cursor(dictionary=True) as cur:
            cur.execute(query)
            rows = cur.fetchall()
        return OnlySelectOutput(rows=rows)