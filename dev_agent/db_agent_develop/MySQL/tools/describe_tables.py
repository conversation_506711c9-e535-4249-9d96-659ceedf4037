from typing import Dict, Any, Union
from pydantic import Field
from interfaces.tool import BaseToolInput, BaseToolOutput, Tool
from services.mysql_connection import MySQL_Service

class DescribeTableInput(BaseToolInput):
    """Input schema for DescribeTableTool.

    Attributes:
        table (str): The name of the table to be described.
    """
    table: str = Field(..., description="The name of the table to describe.")

class DescribeTableOutput(BaseToolOutput):
    """Output schema for DescribeTableTool.

    Attributes:
        columns (list[dict]): The column structure information of the table.
    """
    columns: list[dict] = Field(..., description="Column structure information.")

class DescribeTableTool(Tool):
    """Tool to show the structure of a specific table (columns and their info)."""

    name = "describe_table"
    description = "Show the structure of a specific table."
    input_model = DescribeTableInput
    output_model = DescribeTableOutput

    def get_schema(self) -> Dict[str, Any]:
        """Return the JSON schema for this tool."""
        return {
            "name": self.name,
            "description": self.description,
            "input": self.input_model.model_json_schema(),
            "output": self.output_model.model_json_schema(),
        }
    
    def execute(self, params: DescribeTableInput, **kwargs) -> DescribeTableOutput:
        """
        Execute the tool to describe the structure of a specified table.

        Args:
            params (DescribeTableInput): The input containing the table name.
            **kwargs: Additional keyword arguments (not used).

        Returns:
            DescribeTableOutput: The output with column structure information.
        """
        table = params.table.replace("`", "``")
        with MySQL_Service().get_conn() as conn, conn.cursor(dictionary=True) as cur:
            # Get all tables
            cur.execute("SHOW TABLES")
            table_names = [row[f"Tables_in_{conn.database}"] for row in cur.fetchall()]
            if table not in table_names:
                raise ValueError(f"Table `{table}` does not exist.")

            # Describe columns of the specified table
            cur.execute(f"SHOW COLUMNS FROM `{table}`;")
            columns = cur.fetchall()
        return DescribeTableOutput(columns=columns)
