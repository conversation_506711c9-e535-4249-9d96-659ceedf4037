from typing import Any, Dict, Union
from pydantic import Field, BaseModel, ConfigDict

from atomic_agents.agents.base_agent import BaseIOSchema
from interfaces.tool import Tool, BaseToolInput, ToolResponse
from services.mysql_connection import MySQL_Service

class MySQLAgentInput(BaseIOSchema):
    """Input schema for the MySQL agent tool."""

    query: str = Field(..., description="The SQL query to execute on the MySQL server.")

class MySQLAgentOutput(BaseModel):
    """Output schema for the MySQL agent tool."""

    result: Any = Field(..., description="The query result or error message.")

class MySQLAgentTool(Tool):
    """Tool that executes SQL queries on a MySQL database."""

    name = "mysql_agent"
    description = "Executes a SQL query on the MySQL database and returns the result."
    input_model = MySQLAgentInput
    output_model = MySQLAgentOutput

    async def execute(self, input_data: MySQLAgentInput) -> ToolResponse:
        """Execute the SQL query and return the results."""
        service = MySQL_Service()
        try:
            conn = service.get_conn()
            cursor = conn.cursor()
            cursor.execute(input_data.query)
            if cursor.description:
                result = cursor.fetchall()
            else:
                result = f"Affected rows: {cursor.rowcount}"
            cursor.close()
            conn.close()
            output = MySQLAgentOutput(result=result)
            return ToolResponse.from_model(output)
        except Exception as e:
            output = MySQLAgentOutput(result=f"[ERROR] {e}")
            return ToolResponse.from_model(output)