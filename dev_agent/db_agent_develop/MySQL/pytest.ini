[pytest]
# Additional pytest configuration for MySQL module
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Custom markers for MySQL tests
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests requiring full stack
    real_db: marks tests that require real MySQL database connection
    real_llm: marks tests that require real LLM connection at vLLM endpoint
    schema: marks tests specifically for schema validation
    mock: marks tests that use mocked dependencies

# Test discovery patterns
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    -ra

# Async test configuration
asyncio_mode = auto

# Timeout settings
timeout = 30
timeout_method = thread

# Coverage settings (optional)
# --cov=tools
# --cov=services
# --cov=interfaces
# --cov-report=html
# --cov-report=term-missing