Starting HTTP server...
 * Serving Flask app 'server'
 * Debug mode: off
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
[33mPress CTRL+C to quit[0m
127.0.0.1 - - [06/Nov/2025 02:50:42] "POST /alert HTTP/1.1" 200 -
