WARNING:  Current configuration will not reload as not all conditions are met, please refer to documentation.
INFO:     Started server process [645285]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:6970 (Press CTRL+C to quit)
INFO:     127.0.0.1:45938 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45942 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:45942 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:45942 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 03:56:17] INFO     Processing request of type server.py:674
                             ListToolsRequest
INFO:     127.0.0.1:45942 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
                    INFO     Processing request of type server.py:674
                             ListPromptsRequest
INFO:     127.0.0.1:45942 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
                    INFO     Processing request of type server.py:674
                             ListResourcesRequest
INFO:     127.0.0.1:45942 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 03:56:18] INFO     Processing request of type server.py:674
                             CallToolRequest
INFO:     127.0.0.1:45942 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 03:56:19] INFO     Processing request of type server.py:674
                             CallToolRequest
********************
output: weighted_shortage_index=1.0 error=None
INFO:     127.0.0.1:47162 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 04:52:31] INFO     Processing request of type server.py:674
                             CallToolRequest
INFO:     127.0.0.1:47162 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
                    INFO     Processing request of type server.py:674
                             CallToolRequest
********************
output: weighted_shortage_index=1.0 error=None
INFO:     127.0.0.1:47304 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 04:55:42] INFO     Processing request of type server.py:674
                             CallToolRequest
INFO:     127.0.0.1:47304 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
                    INFO     Processing request of type server.py:674
                             CallToolRequest
********************
output: weighted_shortage_index=1.0 error=None
INFO:     127.0.0.1:47592 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 05:03:09] INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
INFO:     127.0.0.1:47592 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
                    INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
********************
output: weighted_shortage_index=1.0 error=None
INFO:     127.0.0.1:47592 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 05:03:12] INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
INFO:     127.0.0.1:47592 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
                    INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
********************
output: weighted_shortage_index=1.0 error=None
INFO:     127.0.0.1:47932 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
[11/06/25 05:08:23] INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
INFO:     127.0.0.1:47932 - "POST /messages/?session_id=626da4590abf4a5f87cd564a828be13f HTTP/1.1" 202 Accepted
                    INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
********************
output: weighted_shortage_index=1.0 error=None
INFO:     127.0.0.1:48970 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:48972 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:48972 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:48972 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
[11/06/25 05:17:13] INFO     Processing request server.py:674
                             of type
                             ListToolsRequest
INFO:     127.0.0.1:48972 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
                    INFO     Processing request server.py:674
                             of type
                             ListPromptsRequest
INFO:     127.0.0.1:48972 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
                    INFO     Processing request server.py:674
                             of type
                             ListResourcesReque
                             st
INFO:     127.0.0.1:48972 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
[11/06/25 05:17:14] INFO     Processing request server.py:674
                             of type
                             CallToolRequest
INFO:     127.0.0.1:48972 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
                    INFO     Processing request server.py:674
                             of type
                             CallToolRequest
********************
output: weighted_shortage_index=1.0 error=None
INFO:     127.0.0.1:53272 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
[11/06/25 06:20:52] INFO     Processing request of type CallToolRequest      server.py:674
INFO:     127.0.0.1:53272 - "POST /messages/?session_id=015f3448700f48cfb6d27cd7ce944bef HTTP/1.1" 202 Accepted
                    INFO     Processing request of type CallToolRequest      server.py:674
