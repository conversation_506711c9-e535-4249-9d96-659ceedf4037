🚀 SSE MCP Server ready at http://127.0.0.1:8703/sse
INFO:     Started server process [526806]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8702 (Press CTRL+C to quit)
INFO:     127.0.0.1:33228 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33230 - "POST /messages/?session_id=bbd6beb155214b679f294ecdae1568ee HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33230 - "POST /messages/?session_id=bbd6beb155214b679f294ecdae1568ee HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33230 - "POST /messages/?session_id=bbd6beb155214b679f294ecdae1568ee HTTP/1.1" 202 Accepted
[11/06/25 01:03:44] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33242 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33244 - "POST /messages/?session_id=650c2a3c817542a2b51d444cd629b6d2 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33244 - "POST /messages/?session_id=650c2a3c817542a2b51d444cd629b6d2 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33244 - "POST /messages/?session_id=650c2a3c817542a2b51d444cd629b6d2 HTTP/1.1" 202 Accepted
[11/06/25 01:04:13] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33244 - "POST /messages/?session_id=650c2a3c817542a2b51d444cd629b6d2 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33250 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33252 - "POST /messages/?session_id=762029b1072e4b6b841bd1928d4c4f97 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33252 - "POST /messages/?session_id=762029b1072e4b6b841bd1928d4c4f97 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33252 - "POST /messages/?session_id=762029b1072e4b6b841bd1928d4c4f97 HTTP/1.1" 202 Accepted
[11/06/25 01:04:22] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33252 - "POST /messages/?session_id=762029b1072e4b6b841bd1928d4c4f97 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33272 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33274 - "POST /messages/?session_id=7ab752ec461c4e668e7bcc19b4c6954a HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33274 - "POST /messages/?session_id=7ab752ec461c4e668e7bcc19b4c6954a HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33274 - "POST /messages/?session_id=7ab752ec461c4e668e7bcc19b4c6954a HTTP/1.1" 202 Accepted
[11/06/25 01:04:45] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33274 - "POST /messages/?session_id=7ab752ec461c4e668e7bcc19b4c6954a HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33710 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33712 - "POST /messages/?session_id=811a74daa8e647289f825e29d0a84749 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33712 - "POST /messages/?session_id=811a74daa8e647289f825e29d0a84749 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33712 - "POST /messages/?session_id=811a74daa8e647289f825e29d0a84749 HTTP/1.1" 202 Accepted
[11/06/25 01:25:54] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33712 - "POST /messages/?session_id=811a74daa8e647289f825e29d0a84749 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33732 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33734 - "POST /messages/?session_id=0e944a99d6564131860707361a25a3b5 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33734 - "POST /messages/?session_id=0e944a99d6564131860707361a25a3b5 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33734 - "POST /messages/?session_id=0e944a99d6564131860707361a25a3b5 HTTP/1.1" 202 Accepted
[11/06/25 01:26:15] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33734 - "POST /messages/?session_id=0e944a99d6564131860707361a25a3b5 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33746 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33748 - "POST /messages/?session_id=dd7adfd6126f4b1bbeaa2128eb484fe4 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33748 - "POST /messages/?session_id=dd7adfd6126f4b1bbeaa2128eb484fe4 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33748 - "POST /messages/?session_id=dd7adfd6126f4b1bbeaa2128eb484fe4 HTTP/1.1" 202 Accepted
[11/06/25 01:26:27] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33748 - "POST /messages/?session_id=dd7adfd6126f4b1bbeaa2128eb484fe4 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33776 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33778 - "POST /messages/?session_id=a7f820952c81470686b7f19abec6b516 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33778 - "POST /messages/?session_id=a7f820952c81470686b7f19abec6b516 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33778 - "POST /messages/?session_id=a7f820952c81470686b7f19abec6b516 HTTP/1.1" 202 Accepted
[11/06/25 01:26:43] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33778 - "POST /messages/?session_id=a7f820952c81470686b7f19abec6b516 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33786 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33788 - "POST /messages/?session_id=3ee0c970b6b040bbaef6821460ff3a22 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33788 - "POST /messages/?session_id=3ee0c970b6b040bbaef6821460ff3a22 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33788 - "POST /messages/?session_id=3ee0c970b6b040bbaef6821460ff3a22 HTTP/1.1" 202 Accepted
[11/06/25 01:27:04] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33788 - "POST /messages/?session_id=3ee0c970b6b040bbaef6821460ff3a22 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33794 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33796 - "POST /messages/?session_id=3724aee14cbe47968984c58a42aea4f9 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33796 - "POST /messages/?session_id=3724aee14cbe47968984c58a42aea4f9 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33796 - "POST /messages/?session_id=3724aee14cbe47968984c58a42aea4f9 HTTP/1.1" 202 Accepted
[11/06/25 01:27:23] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33796 - "POST /messages/?session_id=3724aee14cbe47968984c58a42aea4f9 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33804 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33806 - "POST /messages/?session_id=f07e8831e23b4e0691ad56b0d296603e HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33806 - "POST /messages/?session_id=f07e8831e23b4e0691ad56b0d296603e HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33806 - "POST /messages/?session_id=f07e8831e23b4e0691ad56b0d296603e HTTP/1.1" 202 Accepted
[11/06/25 01:27:32] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33806 - "POST /messages/?session_id=f07e8831e23b4e0691ad56b0d296603e HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33816 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33818 - "POST /messages/?session_id=7585bcb157984ffa98e06b15254ff9d8 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33818 - "POST /messages/?session_id=7585bcb157984ffa98e06b15254ff9d8 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33818 - "POST /messages/?session_id=7585bcb157984ffa98e06b15254ff9d8 HTTP/1.1" 202 Accepted
[11/06/25 01:27:41] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33818 - "POST /messages/?session_id=7585bcb157984ffa98e06b15254ff9d8 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33824 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33826 - "POST /messages/?session_id=b8a60684216c4b908f4979fb409d7156 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33826 - "POST /messages/?session_id=b8a60684216c4b908f4979fb409d7156 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33826 - "POST /messages/?session_id=b8a60684216c4b908f4979fb409d7156 HTTP/1.1" 202 Accepted
[11/06/25 01:27:50] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33826 - "POST /messages/?session_id=b8a60684216c4b908f4979fb409d7156 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     127.0.0.1:33836 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:33838 - "POST /messages/?session_id=d94bc86c47034fe780c34f2773a90873 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33838 - "POST /messages/?session_id=d94bc86c47034fe780c34f2773a90873 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:33838 - "POST /messages/?session_id=d94bc86c47034fe780c34f2773a90873 HTTP/1.1" 202 Accepted
[11/06/25 01:28:03] INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             Call
                             Tool
                             Requ
                             est
INFO:     127.0.0.1:33838 - "POST /messages/?session_id=d94bc86c47034fe780c34f2773a90873 HTTP/1.1" 202 Accepted
                    INFO     Proc server.py:674
                             essi
                             ng
                             requ
                             est
                             of
                             type
                             List
                             Tool
                             sReq
                             uest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
