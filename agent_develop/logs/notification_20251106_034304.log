WARNING:  Current configuration will not reload as not all conditions are met, please refer to documentation.
INFO:     Started server process [645287]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:6972 (Press CTRL+C to quit)
INFO:     127.0.0.1:60184 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:60186 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:60186 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:60186 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
[11/06/25 03:56:19] INFO     Processing request of type server.py:674
                             ListToolsRequest
INFO:     127.0.0.1:60186 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type server.py:674
                             ListPromptsRequest
INFO:     127.0.0.1:60186 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type server.py:674
                             ListResourcesRequest
INFO:     127.0.0.1:60186 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type server.py:674
                             CallToolRequest
DEBUG: HttpNotification wrapper called with alert_message=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures, subject=[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert, content=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures
DEBUG: execute_tool called with tool_name=HttpNotification, input_data={'alert_message': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures', 'subject': '[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert', 'content': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures'}
DEBUG: input_model created: {"alert_message":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures"}
DEBUG: tool execution result: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: _serialize_response called with response=content=[NotificationContent(type='text', content_id=None, text='✅ HTTP notification sent successfully', json_data=None, model=None)], type: <class 'notification.interfaces.notification.NotificationResponse'>
DEBUG: Single content item processed: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: HttpNotification returning: ✅ HTTP notification sent successfully, type: <class 'str'>
INFO:     127.0.0.1:33172 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
[11/06/25 04:52:31] INFO     Processing request of type server.py:674
                             CallToolRequest
DEBUG: HttpNotification wrapper called with alert_message=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures, subject=[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert, content=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures
DEBUG: execute_tool called with tool_name=HttpNotification, input_data={'alert_message': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures', 'subject': '[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert', 'content': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures'}
DEBUG: input_model created: {"alert_message":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures"}
DEBUG: tool execution result: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: _serialize_response called with response=content=[NotificationContent(type='text', content_id=None, text='✅ HTTP notification sent successfully', json_data=None, model=None)], type: <class 'notification.interfaces.notification.NotificationResponse'>
DEBUG: Single content item processed: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: HttpNotification returning: ✅ HTTP notification sent successfully, type: <class 'str'>
INFO:     127.0.0.1:33314 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
[11/06/25 04:55:42] INFO     Processing request of type server.py:674
                             CallToolRequest
DEBUG: HttpNotification wrapper called with alert_message=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures, subject=[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert, content=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures
DEBUG: execute_tool called with tool_name=HttpNotification, input_data={'alert_message': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures', 'subject': '[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert', 'content': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures'}
DEBUG: input_model created: {"alert_message":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures"}
DEBUG: tool execution result: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: _serialize_response called with response=content=[NotificationContent(type='text', content_id=None, text='✅ HTTP notification sent successfully', json_data=None, model=None)], type: <class 'notification.interfaces.notification.NotificationResponse'>
DEBUG: Single content item processed: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: HttpNotification returning: ✅ HTTP notification sent successfully, type: <class 'str'>
INFO:     127.0.0.1:33602 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
[11/06/25 05:03:09] INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
DEBUG: HttpNotification wrapper called with alert_message=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures, subject=[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert, content=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures
DEBUG: execute_tool called with tool_name=HttpNotification, input_data={'alert_message': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures', 'subject': '[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert', 'content': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures'}
DEBUG: input_model created: {"alert_message":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures"}
DEBUG: tool execution result: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: _serialize_response called with response=content=[NotificationContent(type='text', content_id=None, text='✅ HTTP notification sent successfully', json_data=None, model=None)], type: <class 'notification.interfaces.notification.NotificationResponse'>
DEBUG: Single content item processed: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: HttpNotification returning: ✅ HTTP notification sent successfully, type: <class 'str'>
INFO:     127.0.0.1:33602 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
[11/06/25 05:03:12] INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
DEBUG: HttpNotification wrapper called with alert_message=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures, subject=[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert, content=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures
DEBUG: execute_tool called with tool_name=HttpNotification, input_data={'alert_message': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures', 'subject': '[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert', 'content': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures'}
DEBUG: input_model created: {"alert_message":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures"}
DEBUG: tool execution result: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: _serialize_response called with response=content=[NotificationContent(type='text', content_id=None, text='✅ HTTP notification sent successfully', json_data=None, model=None)], type: <class 'notification.interfaces.notification.NotificationResponse'>
DEBUG: Single content item processed: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: HttpNotification returning: ✅ HTTP notification sent successfully, type: <class 'str'>
INFO:     127.0.0.1:33942 - "POST /messages/?session_id=c07f0223253c4b64aced814cd30bc521 HTTP/1.1" 202 Accepted
[11/06/25 05:08:23] INFO     Pro server.py:674
                             ces
                             sin
                             g
                             req
                             ues
                             t
                             of
                             typ
                             e
                             Cal
                             lTo
                             olR
                             equ
                             est
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
DEBUG: HttpNotification wrapper called with alert_message=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures, subject=[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert, content=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures
DEBUG: execute_tool called with tool_name=HttpNotification, input_data={'alert_message': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures', 'subject': '[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert', 'content': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures'}
DEBUG: input_model created: {"alert_message":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures"}
DEBUG: tool execution result: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: _serialize_response called with response=content=[NotificationContent(type='text', content_id=None, text='✅ HTTP notification sent successfully', json_data=None, model=None)], type: <class 'notification.interfaces.notification.NotificationResponse'>
DEBUG: Single content item processed: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: HttpNotification returning: ✅ HTTP notification sent successfully, type: <class 'str'>
INFO:     127.0.0.1:34986 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:34988 - "POST /messages/?session_id=26481e691bcb4da086b6cf11e5379b59 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:34988 - "POST /messages/?session_id=26481e691bcb4da086b6cf11e5379b59 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:34988 - "POST /messages/?session_id=26481e691bcb4da086b6cf11e5379b59 HTTP/1.1" 202 Accepted
[11/06/25 05:17:14] INFO     Processing request server.py:674
                             of type
                             ListToolsRequest
INFO:     127.0.0.1:34988 - "POST /messages/?session_id=26481e691bcb4da086b6cf11e5379b59 HTTP/1.1" 202 Accepted
                    INFO     Processing request server.py:674
                             of type
                             ListPromptsRequest
INFO:     127.0.0.1:34988 - "POST /messages/?session_id=26481e691bcb4da086b6cf11e5379b59 HTTP/1.1" 202 Accepted
                    INFO     Processing request server.py:674
                             of type
                             ListResourcesReque
                             st
INFO:     127.0.0.1:34988 - "POST /messages/?session_id=26481e691bcb4da086b6cf11e5379b59 HTTP/1.1" 202 Accepted
                    INFO     Processing request server.py:674
                             of type
                             CallToolRequest
DEBUG: HttpNotification wrapper called with alert_message=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures, subject=[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert, content=CRITICAL - Supply Chain Risk Alert
Company: WebSocketOrchestration
Metric: Shortage Index
Current Value: 1.000
Threshold: 0.550
Variance: Within acceptable range
Context: Supply chain disruption detected. Review supplier performance and inventory levels.
IMMEDIATE ACTION REQUIRED:
• Activate supply chain contingency plans
• Contact alternative suppliers immediately
• Assess inventory levels and production impact
• Implement emergency procurement procedures
DEBUG: execute_tool called with tool_name=HttpNotification, input_data={'alert_message': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures', 'subject': '[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert', 'content': 'CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures'}
DEBUG: input_model created: {"alert_message":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures"}
DEBUG: tool execution result: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: _serialize_response called with response=content=[NotificationContent(type='text', content_id=None, text='✅ HTTP notification sent successfully', json_data=None, model=None)], type: <class 'notification.interfaces.notification.NotificationResponse'>
DEBUG: Single content item processed: ✅ HTTP notification sent successfully, type: <class 'str'>
DEBUG: HttpNotification returning: ✅ HTTP notification sent successfully, type: <class 'str'>
INFO:     127.0.0.1:39282 - "POST /messages/?session_id=26481e691bcb4da086b6cf11e5379b59 HTTP/1.1" 202 Accepted
[11/06/25 06:20:52] INFO     Processing request of type CallToolRequest      server.py:674
