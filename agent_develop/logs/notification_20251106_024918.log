WARNING:  Current configuration will not reload as not all conditions are met, please refer to documentation.
INFO:     Started server process [613647]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:6972 (Press CTRL+C to quit)
INFO:     127.0.0.1:57464 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:57466 - "POST /messages/?session_id=19e3f3bf29064b75a2337bc7873cf308 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57466 - "POST /messages/?session_id=19e3f3bf29064b75a2337bc7873cf308 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57466 - "POST /messages/?session_id=19e3f3bf29064b75a2337bc7873cf308 HTTP/1.1" 202 Accepted
[11/06/25 02:50:42] INFO     Processing request   server.py:674
                             of type
                             ListToolsRequest
INFO:     127.0.0.1:57466 - "POST /messages/?session_id=19e3f3bf29064b75a2337bc7873cf308 HTTP/1.1" 202 Accepted
                    INFO     Processing request   server.py:674
                             of type
                             ListPromptsRequest
INFO:     127.0.0.1:57466 - "POST /messages/?session_id=19e3f3bf29064b75a2337bc7873cf308 HTTP/1.1" 202 Accepted
                    INFO     Processing request   server.py:674
                             of type
                             ListResourcesRequest
INFO:     127.0.0.1:57466 - "POST /messages/?session_id=19e3f3bf29064b75a2337bc7873cf308 HTTP/1.1" 202 Accepted
                    INFO     Processing request   server.py:674
                             of type
                             CallToolRequest
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
