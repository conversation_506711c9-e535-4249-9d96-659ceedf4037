#!/usr/bin/env python3
"""
Multi-Server Launcher for MCP Services
Launches and monitors all MCP servers concurrently
"""

import subprocess
import threading
import signal
import sys
import os
from datetime import datetime
from pathlib import Path

# Server configurations
SERVERS = [
    {
        "name": "HTTP",
        "script": "/home/<USER>/agent_develop/http/server.py",
        "cwd": "/home/<USER>/agent_develop/http"
    },
    {
        "name": "INDEX",
        "script": "/home/<USER>/agent_develop/index/server.py",
        "cwd": "/home/<USER>/agent_develop/index"
    },
    {
        "name": "NOTIFICATION",
        "script": "/home/<USER>/agent_develop/notification/server.py",
        "cwd": "/home/<USER>/agent_develop/notification"
    },
    {
        "name": "MYSQL",
        "script": "/home/<USER>/dev_agent/db_agent_develop/MySQL/server.py",
        "cwd": "/home/<USER>/dev_agent/db_agent_develop/MySQL"
    }
]

# Global process list and shutdown flag
processes = []
shutdown_event = threading.Event()
log_dir = Path("logs")

def setup_logging():
    """Create logs directory if it doesn't exist"""
    log_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return timestamp

def stream_output(process, server_name, log_file):
    """Stream output from a process to console and log file"""
    try:
        for line in iter(process.stdout.readline, b''):
            if shutdown_event.is_set():
                break

            decoded_line = line.decode('utf-8', errors='replace').rstrip()
            if decoded_line:
                # Console output with prefix
                print(f"[{server_name}] {decoded_line}", flush=True)

                # Write to log file
                if log_file:
                    log_file.write(f"{decoded_line}\n")
                    log_file.flush()
    except Exception as e:
        if not shutdown_event.is_set():
            print(f"[{server_name}] Error streaming output: {e}", file=sys.stderr)

def launch_server(config, timestamp):
    """Launch a single server and set up output streaming"""
    name = config["name"]
    script = config["script"]
    cwd = config["cwd"]

    # Check if script exists
    if not os.path.exists(script):
        print(f"[{name}] ERROR: Script not found at {script}", file=sys.stderr)
        return None

    try:
        print(f"[{name}] Starting server: {script}")

        # Open log file
        log_file = open(log_dir / f"{name.lower()}_{timestamp}.log", "w")

        # Launch process
        process = subprocess.Popen(
            [sys.executable, script],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            cwd=cwd,
            bufsize=1
        )

        # Start output streaming thread
        thread = threading.Thread(
            target=stream_output,
            args=(process, name, log_file),
            daemon=True
        )
        thread.start()

        print(f"[{name}] Started with PID {process.pid}")

        return {
            "process": process,
            "name": name,
            "thread": thread,
            "log_file": log_file
        }

    except Exception as e:
        print(f"[{name}] ERROR: Failed to start: {e}", file=sys.stderr)
        return None

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n[LAUNCHER] Shutdown signal received. Stopping all servers...")
    shutdown_event.set()
    cleanup()
    sys.exit(0)

def cleanup():
    """Terminate all server processes"""
    for server in processes:
        if server and server["process"].poll() is None:
            name = server["name"]
            print(f"[{name}] Terminating...")
            try:
                server["process"].terminate()
                server["process"].wait(timeout=5)
                print(f"[{name}] Stopped")
            except subprocess.TimeoutExpired:
                print(f"[{name}] Force killing...")
                server["process"].kill()
                server["process"].wait()

            # Close log file
            if server["log_file"]:
                server["log_file"].close()

def monitor_processes():
    """Monitor server processes for unexpected exits"""
    while not shutdown_event.is_set():
        for server in processes:
            if server:
                retcode = server["process"].poll()
                if retcode is not None:
                    name = server["name"]
                    print(f"[{name}] ERROR: Process exited with code {retcode}", file=sys.stderr)

        shutdown_event.wait(1)

def main():
    """Main launcher function"""
    print("=" * 60)
    print("MCP Multi-Server Launcher")
    print("=" * 60)

    # Setup
    timestamp = setup_logging()
    print(f"Log directory: {log_dir.absolute()}")
    print()

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Launch all servers
    for config in SERVERS:
        server = launch_server(config, timestamp)
        processes.append(server)

    print()
    print("=" * 60)
    print("All servers launched. Press Ctrl+C to stop.")
    print("=" * 60)
    print()

    # Monitor processes
    try:
        monitor_processes()
    except KeyboardInterrupt:
        pass
    finally:
        cleanup()

if __name__ == "__main__":
    main()
