WARNING:  Current configuration will not reload as not all conditions are met, please refer to documentation.
INFO:     Started server process [370655]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:6970 (Press CTRL+C to quit)
INFO:     127.0.0.1:57590 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:57592 - "POST /messages/?session_id=128eccfa8d5d4f469c062807f8bbad2e HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57592 - "POST /messages/?session_id=128eccfa8d5d4f469c062807f8bbad2e HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57592 - "POST /messages/?session_id=128eccfa8d5d4f469c062807f8bbad2e HTTP/1.1" 202 Accepted
[11/05/25 00:34:40] INFO     Processing request of type ListToolsRequest                                                                                                                     server.py:674
INFO:     127.0.0.1:57592 - "POST /messages/?session_id=128eccfa8d5d4f469c062807f8bbad2e HTTP/1.1" 202 Accepted
                    INFO     Processing request of type ListPromptsRequest                                                                                                                   server.py:674
INFO:     127.0.0.1:57592 - "POST /messages/?session_id=128eccfa8d5d4f469c062807f8bbad2e HTTP/1.1" 202 Accepted
                    INFO     Processing request of type ListResourcesRequest                                                                                                                 server.py:674
INFO:     127.0.0.1:57630 - "POST /messages/?session_id=128eccfa8d5d4f469c062807f8bbad2e HTTP/1.1" 202 Accepted
[11/05/25 00:35:32] INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
INFO:     127.0.0.1:57630 - "POST /messages/?session_id=128eccfa8d5d4f469c062807f8bbad2e HTTP/1.1" 202 Accepted
                    INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
********************
output: weighted_shortage_index=0.9937499999999999 error=None
INFO:     127.0.0.1:57806 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
[11/05/25 00:39:00] INFO     Processing request of type ListToolsRequest                                                                                                                     server.py:674
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type ListPromptsRequest                                                                                                                   server.py:674
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type ListResourcesRequest                                                                                                                 server.py:674
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
[11/05/25 00:39:01] INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
********************
output: weighted_shortage_index=0.5 error=None
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
********************
output: weighted_shortage_index=0.5 error=None
INFO:     127.0.0.1:57808 - "POST /messages/?session_id=363ebf98961541569aae0ef6d45b5528 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
********************
output: weighted_shortage_index=0.5 error=None
INFO:     127.0.0.1:57840 - "GET /sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
[11/05/25 00:39:46] INFO     Processing request of type ListToolsRequest                                                                                                                     server.py:674
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type ListPromptsRequest                                                                                                                   server.py:674
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type ListResourcesRequest                                                                                                                 server.py:674
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
[11/05/25 00:39:47] INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
********************
output: weighted_shortage_index=0.5 error=None
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
********************
output: weighted_shortage_index=0.30000000000000004 error=None
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
INFO:     127.0.0.1:57842 - "POST /messages/?session_id=f4c8a0ae1bca4276ae4b8b186c8141c6 HTTP/1.1" 202 Accepted
                    INFO     Processing request of type CallToolRequest                                                                                                                      server.py:674
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 76, in app
    await response(scope, receive, send)
TypeError: 'NoneType' object is not callable
INFO:     Shutting down
INFO:     Waiting for connections to close. (CTRL+C to force quit)
ERROR:    Cancel 1 running task(s), timeout graceful shutdown exceeded
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [370655]
