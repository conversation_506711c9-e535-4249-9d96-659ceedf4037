{"level":"INFO","timestamp":"2025-11-04T07:22:16.144615","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:16.146114","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:16.146147","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:16.146286","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:16.146304","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:16.146321","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.146345","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"921a50b0-dc99-404e-989a-03ea4fc0e7c6"}}}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.290982","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.291232","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.291444","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.291483","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.291563","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.291579","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.291924","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292149","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292225","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292240","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292251","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292264","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292283","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292390","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292403","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292415","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292428","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292437","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292457","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292479","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292491","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292508","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292518","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-11-04T07:22:16.292614","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.405136","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.405321","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.405348","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.405372","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.405390","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.405407","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.405428","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_debug_user","agent_name":"mcp_application_loop","session_id":"b14ec458-4cb2-489b-9e8d-42f1ff2f54ad"}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.405594","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.406429","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.406463","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Financial session initialized for user debug_user, type: orchestration"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.408907","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.408932","namespace":"orchestrator.orchestration_runner","message":"Query: The order from a customer needs 10000 units of GPU MM2004IC001. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient."}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.408944","namespace":"orchestrator.orchestration_runner","message":"Early stream callback provided for financial query execution"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.408954","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.409175","namespace":"orchestrator.query_processor","message":"Processing financial query: The order from a customer needs 10000 units of GPU MM2004IC001. Produce shortage indices and escalat..."}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.417608","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.17, workflow: shortage_only)"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.417637","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.17)"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:28.418807","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_3eb603ed.json"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.418837","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.419300","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.419325","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.420104","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.420123","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.420147","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.420171","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.420181","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-11-04T07:22:28.420913","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-11-04T07:22:41.599087","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_start | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-04T07:22:41.599641","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_chunk | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-04T07:22:41.600389","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_complete | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-04T07:22:41.601298","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:41.670836","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 1 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-04T07:22:41.671942","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:22:49.401765","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:22:49.469995","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 2 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-04T07:22:49.470572","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:23:13.846870","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:23:13.917719","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 3 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-04T07:23:13.918939","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:23:21.961997","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:23:22.116029","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 4 produced result: \"{\\n  \\\"content\\\": [\\n    {\\n      \\\"type\\\": \\\"json\\\",\\n      \\\"content_id\\\": null,\\n      \\\"text\\\": null,\\n      \\\"json_data\\\": {\\n        \\\"result\\\": [\\n          [\\n            \\\"BILLS_OF_MATERIALS\\\"\\n          ],\\n          [\\n            \\\"CUSTOMERS\\\"\\n          ],\\n          [\\n            \\\"CUSTOMER_ORDERS\\\"\\n          ],\\n          [\\n            \\\"CUSTOMER_ORDER_DETAIL\\\"\\n          ],\\n          [\\n            \\\"EMPLOYEES\\\"\\n          ],\\n          [\\n            \\\"FACTORIES\\\"\\n          ],\\n          [\\n            \\\"GOODS_RECEIPT\\\"\\n          ],\\n          [\\n            \\\"GOODS_REC... (truncated)"}
{"level":"INFO","timestamp":"2025-11-04T07:23:22.116788","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:23:31.994705","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:23:32.124922","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 5 produced result: \"{\\n  \\\"content\\\": [\\n    {\\n      \\\"type\\\": \\\"json\\\",\\n      \\\"content_id\\\": null,\\n      \\\"text\\\": null,\\n      \\\"json_data\\\": {\\n        \\\"result\\\": [\\n          [\\n            \\\"material_id\\\",\\n            \\\"int\\\",\\n            \\\"NO\\\",\\n            \\\"PRI\\\",\\n            null,\\n            \\\"auto_increment\\\"\\n          ],\\n          [\\n            \\\"material_code\\\",\\n            \\\"varchar(50)\\\",\\n            \\\"NO\\\",\\n            \\\"UNI\\\",\\n            null,\\n            \\\"\\\"\\n          ],\\n          [\\n            \\\"material_name\\\",\\n            \\\"varchar(255)\\\",\\n            \\\"NO\\\",\\n      ... (truncated)"}
{"level":"INFO","timestamp":"2025-11-04T07:23:32.125637","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:23:41.038215","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:23:41.168309","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 6 produced result: \"{\\n  \\\"content\\\": [\\n    {\\n      \\\"type\\\": \\\"json\\\",\\n      \\\"content_id\\\": null,\\n      \\\"text\\\": null,\\n      \\\"json_data\\\": {\\n        \\\"result\\\": [\\n          [\\n            \\\"150.00\\\"\\n          ]\\n        ]\\n      }\\n    }\\n  ]\\n}\""}
{"level":"INFO","timestamp":"2025-11-04T07:23:41.168935","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:23:49.523889","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:23:49.652812","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 7 produced result: \"{\\n  \\\"content\\\": [\\n    {\\n      \\\"type\\\": \\\"json\\\",\\n      \\\"content_id\\\": null,\\n      \\\"text\\\": null,\\n      \\\"json_data\\\": {\\n        \\\"result\\\": [\\n          [\\n            9\\n          ]\\n        ]\\n      }\\n    }\\n  ]\\n}\""}
{"level":"INFO","timestamp":"2025-11-04T07:23:49.653432","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:23:56.126298","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:23:56.255615","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 8 produced result: \"{\\n  \\\"content\\\": [\\n    {\\n      \\\"type\\\": \\\"json\\\",\\n      \\\"content_id\\\": null,\\n      \\\"text\\\": null,\\n      \\\"json_data\\\": {\\n        \\\"result\\\": \\\"[ERROR] 1054 (42S22): Unknown column 'name' in 'field list'\\\"\\n      }\\n    }\\n  ]\\n}\""}
{"level":"INFO","timestamp":"2025-11-04T07:23:56.256256","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:24:06.434839","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:06.575679","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 9 produced result: \"{\\n  \\\"content\\\": [\\n    {\\n      \\\"type\\\": \\\"json\\\",\\n      \\\"content_id\\\": null,\\n      \\\"text\\\": null,\\n      \\\"json_data\\\": {\\n        \\\"result\\\": [\\n          [\\n            \\\"supplier_id\\\",\\n            \\\"int\\\",\\n            \\\"NO\\\",\\n            \\\"PRI\\\",\\n            null,\\n            \\\"auto_increment\\\"\\n          ],\\n          [\\n            \\\"supplier_name\\\",\\n            \\\"varchar(255)\\\",\\n            \\\"NO\\\",\\n            \\\"\\\",\\n            null,\\n            \\\"\\\"\\n          ],\\n          [\\n            \\\"contact_person\\\",\\n            \\\"varchar(100)\\\",\\n            \\\"YES\\\",\\n      ... (truncated)"}
{"level":"INFO","timestamp":"2025-11-04T07:24:06.576374","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:24:14.867301","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:15.003689","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 10 produced result: \"{\\n  \\\"content\\\": [\\n    {\\n      \\\"type\\\": \\\"json\\\",\\n      \\\"content_id\\\": null,\\n      \\\"text\\\": null,\\n      \\\"json_data\\\": {\\n        \\\"result\\\": [\\n          [\\n            \\\"MetaMind Technology Co., Ltd.\\\",\\n            \\\"<EMAIL>\\\"\\n          ]\\n        ]\\n      }\\n    }\\n  ]\\n}\""}
{"level":"INFO","timestamp":"2025-11-04T07:24:15.004697","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.173961","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_error event"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.174794","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_3eb603ed.json"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.174828","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.174846","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 120.75s"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.175517","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.175561","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.175575","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.176184","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_start | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.176509","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_chunk | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.176831","namespace":"orchestrator.workflow_patterns.tool_call_start","message":"Emitting shortage tool event via tool callback | type=%s"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.177903","namespace":"mcp_agent.agents.agent","message":"Initializing agent enhanced_shortage_analyzer_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.180737","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"shortage-index: Found server configuration=","data":{"data":{"name":"shortage-index","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6970/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.180833","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Found server configuration=","data":{"data":{"name":"fetch","description":null,"transport":"stdio","command":"uvx","args":["mcp-server-fetch"],"url":null,"headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.181219","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"shortage-index: Up and running with a persistent connection!"}
{"level":"INFO","timestamp":"2025-11-04T07:24:29.245113","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.255681","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0","websiteUrl":null,"icons":null}}}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.264052","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0","websiteUrl":null,"icons":null}}}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.270094","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.20.0","websiteUrl":null,"icons":null},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.270170","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.271423","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.280950","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"ShortageIndex","title":null,"description":"Calculates the shortage index based on required and available quantities and return shortage index.","inputSchema":{"properties":{"required_qty":{"description":"A list of required quantities","items":{},"title":"Required Qty","type":"array"},"available_qty":{"description":"A list of available quantities","items":{},"title":"Available Qty","type":"array"}},"required":["required_qty","available_qty"],"title":"ShortageIndexArguments","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null},{"name":"WeightedShortageIndex","title":null,"description":"Calculates the weighted shortage index based on required, available quantities, weight factors, and return weighted shortage index.","inputSchema":{"properties":{"required_qty":{"description":"A list of required quantities","items":{},"title":"Required Qty","type":"array"},"available_qty":{"description":"A list of available quantities","items":{},"title":"Available Qty","type":"array"},"weight":{"description":"A list of weight factors corresponding to each quantity","items":{},"title":"Weight","type":"array"}},"required":["required_qty","available_qty","weight"],"title":"WeightedShortageIndexArguments","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.281934","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.288481","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.288995","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.295601","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:29.295703","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"MCP Aggregator initialized for server 'shortage-index'","data":{"data":{"progress_action":"Initialized","server_name":"shortage-index","agent_name":"enhanced_shortage_analyzer_websocketorchestration","tool_count":2,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.099052","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":null,"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"mcp-fetch","title":null,"version":"1.20.0","websiteUrl":null,"icons":null},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.099134","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.100085","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.104454","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"fetch","title":null,"description":"Fetches a URL from the internet and optionally extracts its contents as markdown.\n\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.","inputSchema":{"description":"Parameters for fetching a URL.","properties":{"url":{"description":"URL to fetch","format":"uri","minLength":1,"title":"Url","type":"string"},"max_length":{"default":5000,"description":"Maximum number of characters to return.","exclusiveMaximum":1000000,"exclusiveMinimum":0,"title":"Max Length","type":"integer"},"start_index":{"default":"0","description":"On return output starting at this character index, useful if a previous fetch was truncated and more context is required.","minimum":"0","title":"Start Index","type":"integer"},"raw":{"default":false,"description":"Get the actual HTML content of the requested page, without simplification.","title":"Raw","type":"boolean"}},"required":["url"],"title":"Fetch","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.105019","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.107345","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[{"name":"fetch","title":null,"description":"Fetch a URL and extract its contents as markdown","arguments":[{"name":"url","description":"URL to fetch","required":true}],"icons":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.107725","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Server 'fetch' does not support resources"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.107807","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"MCP Aggregator initialized for server 'fetch'","data":{"data":{"progress_action":"Initialized","server_name":"fetch","agent_name":"enhanced_shortage_analyzer_websocketorchestration","tool_count":1,"prompt_count":"1","resource_count":0}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.108299","namespace":"mcp_agent.agents.agent","message":"Agent enhanced_shortage_analyzer_websocketorchestration initialized."}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.108416","namespace":"mcp_agent.agents.agent","message":"Human input callback not set"}
{"level":"INFO","timestamp":"2025-11-04T07:24:30.108467","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using model from request_params: Qwen/Qwen3-32B"}
{"level":"INFO","timestamp":"2025-11-04T07:24:30.108478","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"VLLMAugmentedLLM selected model: Qwen/Qwen3-32B"}
{"level":"INFO","timestamp":"2025-11-04T07:24:30.108497","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"VLLMAugmentedLLM API arguments model: Qwen/Qwen3-32B"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.108630","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"vLLM request arguments: {'model': 'Qwen/Qwen3-32B', 'messages': [{'role': 'system', 'content': 'You are an Enhanced Shortage Analyzer Agent specializing in comprehensive supply chain risk assessment for WebSocketOrchestration.\\n\\nCORE RESPONSIBILITIES:\\n1. Analyze financial data to identify inventory shortages and supply chain vulnerabilities\\n2. Extract and process required_qty, available_qty, and weight data from multiple sources\\n3. Utilize MCP shortage-index server tools (ShortageIndexTool and WeightedShortageIndexTool)\\n4. Provide detailed risk assessments with quantitative shortage indices\\n5. Generate actionable, prioritized recommendations for supply chain optimization\\n\\nADVANCED ANALYSIS FRAMEWORK:\\n\\nData Processing Capabilities:\\n- Parse structured financial text for inventory patterns\\n- Extract component data from various formats (text, structured data, direct quantities)\\n- Handle multiple input sources: financial_data, components dict, quantity lists\\n- Apply intelligent data validation and sanitization\\n- Support weighted analysis for strategic component prioritization\\n\\nMCP Tool Integration:\\n- Use ShortageIndexTool for basic shortage calculations: shortage_index = 1 - min(available_qty[i] / required_qty[i])\\n- Use WeightedShortageIndexTool for strategic analysis: weighted_shortage = sum(weight[i] * shortage[i])\\n- Implement fallback calculations when MCP services unavailable\\n- Handle tool errors gracefully with comprehensive error recovery\\n\\nRisk Classification System:\\n- LOW Risk (0.0-0.3): Adequate inventory levels, minimal supply chain concerns\\n- MEDIUM Risk (0.3-0.7): Moderate shortages, requires monitoring and proactive measures\\n- HIGH Risk (0.7-1.0): Critical shortages, immediate action required\\n\\nAdvanced Output Generation:\\n- Provide detailed component-by-component analysis\\n- Include both simple and weighted shortage indices when applicable\\n- Generate risk-appropriate recommendations (urgent vs. preventive)\\n- Cite calculation methods and data sources for transparency\\n- Maintain professional tone with quantitative backing\\n\\nError Handling and Resilience:\\n- Gracefully handle MCP service unavailability with local calculations\\n- Validate and sanitize all input data before processing\\n- Provide meaningful error messages and fallback responses\\n- Log processing steps for debugging and improvement\\n\\nData Source Flexibility:\\n- Financial text parsing: \"CPU available is 1, require is 2, weight 0.2\"\\n- Structured components: {\"cpu\": {\"required\": 2, \"available\": 1}}\\n- Direct quantity lists: required_quantities=[2,4], available_quantities=[1,2]\\n- Mixed data sources with intelligent merging and validation\\n\\nQuality Assurance:\\n- Normalize weights to sum to 1.0 for weighted calculations\\n- Ensure non-negative quantities and valid ranges\\n- Cross-validate results between different calculation methods\\n- Provide confidence indicators for analysis quality\\n\\nAlways maintain analytical rigor, provide data-driven insights, and ensure actionable recommendations aligned with risk levels.'}, {'role': 'user', 'content': 'Extract all inventory data from the following MySQL agent response.\\nFor each material mentioned, identify:\\n- Material code (e.g., MM2004IC001, DEP2004IC002)\\n- Available quantity in stock\\n- Required quantity (if mentioned)\\n- Minimum stock level (if mentioned)\\n- Material description (if provided)\\n\\nMySQL Agent Response:\\nWorkflow Analysis: shortage_analysis (shortage_only)\\nOriginal Query: The order from a customer needs 10000 units of GPU MM2004IC001. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient.\\nProgress: Step 1/3\\n\\nMySQL Analysis Results:\\n- Response: Analysis incomplete after 10 iterations\\n- Structured Data:\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\nmysql_agent result: {\\n  \"content\": [\\n    {\\n      \"type\": \"json\",\\n      \"content_id\": null,\\n      \"text\": null,\\n      \"json_data\": {\\n        \"result\": [\\n          [\\n            \"BILLS_OF_MATERIALS\"\\n          ],\\n          [\\n            \"CUSTOMERS\"\\n          ],\\n          [\\n            \"CUSTOMER_ORDERS\"\\n          ],\\n      \\nmysql_agent result: {\\n  \"content\": [\\n    {\\n      \"type\": \"json\",\\n      \"content_id\": null,\\n      \"text\": null,\\n      \"json_data\": {\\n        \"result\": [\\n          [\\n            \"material_id\",\\n            \"int\",\\n            \"NO\",\\n            \"PRI\",\\n            null,\\n            \"auto_increment\"\\n          ],\\n          [\\n \\n\\nExtract all materials with their inventory data. Be precise with quantities and material codes.'}], 'max_tokens': 4096, 'temperature': 0.7, 'stop': None, 'tools': [{'type': 'function', 'function': {'name': 'shortage-index_ShortageIndex', 'description': 'Calculates the shortage index based on required and available quantities and return shortage index.', 'parameters': {'properties': {'required_qty': {'description': 'A list of required quantities', 'items': {}, 'title': 'Required Qty', 'type': 'array'}, 'available_qty': {'description': 'A list of available quantities', 'items': {}, 'title': 'Available Qty', 'type': 'array'}}, 'required': ['required_qty', 'available_qty'], 'title': 'ShortageIndexArguments', 'type': 'object'}}}, {'type': 'function', 'function': {'name': 'shortage-index_WeightedShortageIndex', 'description': 'Calculates the weighted shortage index based on required, available quantities, weight factors, and return weighted shortage index.', 'parameters': {'properties': {'required_qty': {'description': 'A list of required quantities', 'items': {}, 'title': 'Required Qty', 'type': 'array'}, 'available_qty': {'description': 'A list of available quantities', 'items': {}, 'title': 'Available Qty', 'type': 'array'}, 'weight': {'description': 'A list of weight factors corresponding to each quantity', 'items': {}, 'title': 'Weight', 'type': 'array'}}, 'required': ['required_qty', 'available_qty', 'weight'], 'title': 'WeightedShortageIndexArguments', 'type': 'object'}}}, {'type': 'function', 'function': {'name': 'fetch_fetch', 'description': 'Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.', 'parameters': {'description': 'Parameters for fetching a URL.', 'properties': {'url': {'description': 'URL to fetch', 'format': 'uri', 'minLength': 1, 'title': 'Url', 'type': 'string'}, 'max_length': {'default': 5000, 'description': 'Maximum number of characters to return.', 'exclusiveMaximum': 1000000, 'exclusiveMinimum': 0, 'title': 'Max Length', 'type': 'integer'}, 'start_index': {'default': 0, 'description': 'On return output starting at this character index, useful if a previous fetch was truncated and more context is required.', 'minimum': 0, 'title': 'Start Index', 'type': 'integer'}, 'raw': {'default': False, 'description': 'Get the actual HTML content of the requested page, without simplification.', 'title': 'Raw', 'type': 'boolean'}}, 'required': ['url'], 'title': 'Fetch', 'type': 'object'}}}], 'extra_body': {'chat_template_kwargs': {'enable_thinking': False}}}"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:30.108648","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Chat in progress","data":{"data":{"progress_action":"Chatting","model":"Qwen/Qwen3-32B","agent_name":"enhanced_shortage_analyzer_websocketorchestration","chat_turn":1}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.682788","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Raw vLLM API response (asyncio mode):","data":{"data":{"id":"chatcmpl-1ff353cbf3c844b180a3d44f8f046ade","choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"The provided MySQL agent response appears to be incomplete or corrupted, and it does not contain the required inventory data for materials. The response includes errors, incomplete JSON structures, and references to tables without concrete data for materials.\n\nIf you have a corrected or more detailed MySQL agent response, please provide it, and I can extract the relevant inventory information.","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[],"reasoning_content":null},"stop_reason":null}],"created":1762241070,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":69,"prompt_tokens":1663,"total_tokens":1732,"completion_tokens_details":null,"prompt_tokens_details":null},"prompt_logprobs":null,"kv_transfer_params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683042","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"vLLM ChatCompletion response:","data":{"data":{"id":"chatcmpl-1ff353cbf3c844b180a3d44f8f046ade","choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"The provided MySQL agent response appears to be incomplete or corrupted, and it does not contain the required inventory data for materials. The response includes errors, incomplete JSON structures, and references to tables without concrete data for materials.\n\nIf you have a corrected or more detailed MySQL agent response, please provide it, and I can extract the relevant inventory information.","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[]}}],"created":1762241070,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":69,"prompt_tokens":1663,"total_tokens":1732,"completion_tokens_details":null,"prompt_tokens_details":null},"_request_id":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683092","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Iteration 0: Stopping because finish_reason is 'stop'"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683134","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Chat finished","data":{"data":{"progress_action":"Finished","model":"Qwen/Qwen3-32B","agent_name":"enhanced_shortage_analyzer_websocketorchestration"}}}
{"level":"INFO","timestamp":"2025-11-04T07:24:31.683216","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using model from request_params: Qwen/Qwen3-32B"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683244","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"VLLMRequestStructuredCompletionRequest created: <class 'mcp_agent.workflows.llm.augmented_llm_vllm.VLLMRequestStructuredCompletionRequest'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683258","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Response model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683269","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Request object response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683280","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Request object response_model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683290","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Execution engine: asyncio"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683302","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using asyncio direct execution path"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683777","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"_execute_structured_completion_direct called with request type: <class 'mcp_agent.workflows.llm.augmented_llm_vllm.VLLMRequestStructuredCompletionRequest'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683798","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Request response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683814","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Request response_model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683825","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Request serialized_response_model: None"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683836","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Using direct response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683846","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Response model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.683859","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Creating async instructor client with base_url: http://192.168.1.54:38701/v1"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.746806","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Instructor client created: <class 'instructor.core.client.AsyncInstructor'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:31.746833","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"About to call client.chat.completions.create with response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.155456","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Structured response created successfully: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.155492","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Structured response type: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.155506","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Final structured response type: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.168651","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"ShortageIndex","server_name":"shortage-index","agent_name":"enhanced_shortage_analyzer_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.169202","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"ShortageIndex","arguments":{"required_qty":[10000.0],"available_qty":[0.0]}}}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.178611","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"{\n  \"shortage_index\": 1.0,\n  \"error\": null\n}","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.179331","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"WeightedShortageIndex","server_name":"shortage-index","agent_name":"enhanced_shortage_analyzer_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.179816","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"WeightedShortageIndex","arguments":{"required_qty":[10000.0],"available_qty":[0.0],"weight":[1.0]}}}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.186297","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"{\n  \"weighted_shortage_index\": 1.0,\n  \"error\": null\n}","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.187072","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_complete | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.187630","namespace":"orchestrator.workflow_patterns.tool_call_result","message":"Emitting shortage tool event via tool callback | type=%s"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.188937","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_3eb603ed.json"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.188976","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.188997","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 10.01s"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.189289","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.189322","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.189333","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.190140","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.193543","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.194063","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.194433","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.265262","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0","websiteUrl":null,"icons":null}}}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.270085","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.20.0","websiteUrl":null,"icons":null},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.270157","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.271295","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.280056","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.280884","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.286956","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.287295","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.293591","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.293663","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.294136","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.294723","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.295059","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\ud83d\udea8 CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n\u2022 Activate supply chain contingency plans\n\u2022 Contact alternative suppliers immediately\n\u2022 Assess inventory levels and production impact\n\u2022 Implement emergency procurement procedures","subject":"[CRITICAL PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\ud83d\udea8 CRITICAL - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 1.000\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nIMMEDIATE ACTION REQUIRED:\n\u2022 Activate supply chain contingency plans\n\u2022 Contact alternative suppliers immediately\n\u2022 Assess inventory levels and production impact\n\u2022 Implement emergency procurement procedures"}}}}}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.305750","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.307520","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-11-04T07:24:39.307688","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.12/v/list_type"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.307713","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.12s"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.307825","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_3eb603ed completed in 130.89s"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.308599","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_3eb603ed.json"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.308637","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_3eb603ed"}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.308655","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_3eb603ed (success: True, time: 130.90s)"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.372391","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.372596","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Decremented connection ref count to 2"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.372615","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Aggregator closing with ref count 2, connection manager will remain active"}
{"level":"DEBUG","timestamp":"2025-11-04T07:24:39.372649","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-11-04T07:24:39.372690","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_debug_user","agent_name":"mcp_application_loop"}}}
