{"level":"INFO","timestamp":"2025-11-06T00:15:51.521310","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-11-06T00:15:51.522958","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-11-06T00:15:51.522985","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-06T00:15:51.523126","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-06T00:15:51.523149","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-06T00:15:51.523166","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.523187","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"d0dfefcb-e3b5-460c-8681-a319bd4d6272"}}}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.740639","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.740903","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.741131","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.741173","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.741250","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.741266","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.741727","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.741971","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742053","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742073","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742084","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742097","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742112","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742123","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742140","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742150","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742160","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742172","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742185","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742206","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742216","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742234","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742248","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-11-06T00:15:51.742343","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.938697","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.939015","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.939043","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.939071","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.939089","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.939106","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.939125","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_debug_user","agent_name":"mcp_application_loop","session_id":"35300fd6-8e92-4bdd-a758-121b0b715bb1"}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.939289","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.939995","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.940029","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"Financial session initialized for user debug_user, type: orchestration"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.942480","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.942505","namespace":"orchestrator.orchestration_runner","message":"Query: The order from a customer needs 10000 units of GPU MM2004IC001. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient."}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.942516","namespace":"orchestrator.orchestration_runner","message":"Early stream callback provided for financial query execution"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.942526","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.942760","namespace":"orchestrator.query_processor","message":"Processing financial query: The order from a customer needs 10000 units of GPU MM2004IC001. Produce shortage indices and escalat..."}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.951196","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.17, workflow: shortage_only)"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.951225","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.17)"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:02.952047","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_a182a2cb.json"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.952076","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.952543","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.952561","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.953342","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.953360","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.953384","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.953409","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.953420","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-11-06T00:16:02.954256","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-11-06T00:16:25.518989","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_start | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-06T00:16:25.519471","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_chunk | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-06T00:16:25.519787","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_complete | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-06T00:16:25.520575","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:25.623537","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 1 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:16:25.624141","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:16:39.603863","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:39.705869","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 2 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:16:39.706436","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:16:49.557984","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:16:49.629994","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 3 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:16:49.630580","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:17:04.674989","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:17:04.742496","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 4 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:17:04.743101","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:17:12.510688","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:17:12.578199","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 5 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:17:12.578774","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:17:22.902686","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:17:22.975962","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 6 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:17:22.976516","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:17:32.257525","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:17:32.325561","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 7 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:17:32.326151","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:17:42.758150","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:17:42.825770","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 8 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:17:42.826333","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:17:50.733134","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:17:50.800690","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 9 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:17:50.801271","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:18:02.737331","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start for mysql_agent"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:02.804473","namespace":"orchestrator.workflow_patterns","message":"MySQL tool mysql_agent iteration 10 produced result: \"Error: unhandled errors in a TaskGroup (1 sub-exception)\""}
{"level":"INFO","timestamp":"2025-11-06T00:18:02.805050","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result for mysql_agent"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.922103","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:14.922867","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_a182a2cb.json"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.922902","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.922920","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 131.97s"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.923235","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.923268","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.923280","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.923767","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_start | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.924070","namespace":"orchestrator.workflow_patterns","message":"Emitting early stream event | type=vllm_response_chunk | has_workflow_callback=False"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.924354","namespace":"orchestrator.workflow_patterns.tool_call_start","message":"Emitting shortage tool event via tool callback | type=%s"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:14.925300","namespace":"mcp_agent.agents.agent","message":"Initializing agent enhanced_shortage_analyzer_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:14.926369","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"shortage-index: Found server configuration=","data":{"data":{"name":"shortage-index","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6970/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:14.926460","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Found server configuration=","data":{"data":{"name":"fetch","description":null,"transport":"stdio","command":"uvx","args":["mcp-server-fetch"],"url":null,"headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.926762","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"shortage-index: Up and running with a persistent connection!"}
{"level":"INFO","timestamp":"2025-11-06T00:18:14.992524","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.007000","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0","websiteUrl":null,"icons":null}}}}}
{"level":"ERROR","timestamp":"2025-11-06T00:18:15.009477","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"shortage-index: Sub-error 1 in lifecycle task:\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_transports/default.py\", line 101, in map_httpcore_exceptions\n    yield\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_transports/default.py\", line 394, in handle_async_request\n    resp = await self._pool.handle_async_request(req)\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_async/connection_pool.py\", line 256, in handle_async_request\n    raise exc from None\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_async/connection_pool.py\", line 236, in handle_async_request\n    response = await connection.handle_async_request(\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_async/connection.py\", line 101, in handle_async_request\n    raise exc\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_async/connection.py\", line 78, in handle_async_request\n    stream = await self._connect(request)\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_async/connection.py\", line 124, in _connect\n    stream = await self._network_backend.connect_tcp(**kwargs)\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_backends/auto.py\", line 31, in connect_tcp\n    return await self._backend.connect_tcp(\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_backends/anyio.py\", line 113, in connect_tcp\n    with map_exceptions(exc_map):\n  File \"/usr/lib/python3.10/contextlib.py\", line 153, in __exit__\n    self.gen.throw(typ, value, traceback)\n  File \"/usr/local/lib/python3.10/dist-packages/httpcore/_exceptions.py\", line 14, in map_exceptions\n    raise to_exc(exc) from exc\nhttpcore.ConnectError: All connection attempts failed\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.10/dist-packages/mcp/client/sse.py\", line 61, in sse_client\n    async with aconnect_sse(\n  File \"/usr/lib/python3.10/contextlib.py\", line 199, in __aenter__\n    return await anext(self.gen)\n  File \"/usr/local/lib/python3.10/dist-packages/httpx_sse/_api.py\", line 74, in aconnect_sse\n    async with client.stream(method, url, headers=headers, **kwargs) as response:\n  File \"/usr/lib/python3.10/contextlib.py\", line 199, in __aenter__\n    return await anext(self.gen)\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_client.py\", line 1583, in stream\n    response = await self.send(\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_client.py\", line 1629, in send\n    response = await self._send_handling_auth(\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_client.py\", line 1657, in _send_handling_auth\n    response = await self._send_handling_redirects(\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_client.py\", line 1694, in _send_handling_redirects\n    response = await self._send_single_request(request)\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_client.py\", line 1730, in _send_single_request\n    response = await transport.handle_async_request(request)\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_transports/default.py\", line 393, in handle_async_request\n    with map_httpcore_exceptions():\n  File \"/usr/lib/python3.10/contextlib.py\", line 153, in __exit__\n    self.gen.throw(typ, value, traceback)\n  File \"/usr/local/lib/python3.10/dist-packages/httpx/_transports/default.py\", line 118, in map_httpcore_exceptions\n    raise mapped_exc(message) from exc\nhttpx.ConnectError: All connection attempts failed\n"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.848449","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":null,"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"mcp-fetch","title":null,"version":"1.20.0","websiteUrl":null,"icons":null},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.848722","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.850003","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.854467","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"fetch","title":null,"description":"Fetches a URL from the internet and optionally extracts its contents as markdown.\n\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.","inputSchema":{"description":"Parameters for fetching a URL.","properties":{"url":{"description":"URL to fetch","format":"uri","minLength":1,"title":"Url","type":"string"},"max_length":{"default":5000,"description":"Maximum number of characters to return.","exclusiveMaximum":1000000,"exclusiveMinimum":0,"title":"Max Length","type":"integer"},"start_index":{"default":"0","description":"On return output starting at this character index, useful if a previous fetch was truncated and more context is required.","minimum":"0","title":"Start Index","type":"integer"},"raw":{"default":false,"description":"Get the actual HTML content of the requested page, without simplification.","title":"Raw","type":"boolean"}},"required":["url"],"title":"Fetch","type":"object"},"outputSchema":null,"icons":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.855155","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.857510","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[{"name":"fetch","title":null,"description":"Fetch a URL and extract its contents as markdown","arguments":[{"name":"url","description":"URL to fetch","required":true}],"icons":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.857894","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Server 'fetch' does not support resources"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.857984","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"MCP Aggregator initialized for server 'fetch'","data":{"data":{"progress_action":"Initialized","server_name":"fetch","agent_name":"enhanced_shortage_analyzer_websocketorchestration","tool_count":1,"prompt_count":"1","resource_count":0}}}
{"level":"ERROR","timestamp":"2025-11-06T00:18:15.858300","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Error loading server data: MCP Server: 'shortage-index': Failed to initialize with error: 'unhandled errors in a TaskGroup (1 sub-exception)'. Check mcp_agent.config.yaml. Attempting to continue"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.858564","namespace":"mcp_agent.agents.agent","message":"Agent enhanced_shortage_analyzer_websocketorchestration initialized."}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.858671","namespace":"mcp_agent.agents.agent","message":"Human input callback not set"}
{"level":"INFO","timestamp":"2025-11-06T00:18:15.858726","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using model from request_params: Qwen/Qwen3-32B"}
{"level":"INFO","timestamp":"2025-11-06T00:18:15.858737","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"VLLMAugmentedLLM selected model: Qwen/Qwen3-32B"}
{"level":"INFO","timestamp":"2025-11-06T00:18:15.858766","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"VLLMAugmentedLLM API arguments model: Qwen/Qwen3-32B"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.858860","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"vLLM request arguments: {'model': 'Qwen/Qwen3-32B', 'messages': [{'role': 'system', 'content': 'You are an Enhanced Shortage Analyzer Agent specializing in comprehensive supply chain risk assessment for WebSocketOrchestration.\\n\\nCORE RESPONSIBILITIES:\\n1. Analyze financial data to identify inventory shortages and supply chain vulnerabilities\\n2. Extract and process required_qty, available_qty, and weight data from multiple sources\\n3. Utilize MCP shortage-index server tools (ShortageIndexTool and WeightedShortageIndexTool)\\n4. Provide detailed risk assessments with quantitative shortage indices\\n5. Generate actionable, prioritized recommendations for supply chain optimization\\n\\nADVANCED ANALYSIS FRAMEWORK:\\n\\nData Processing Capabilities:\\n- Parse structured financial text for inventory patterns\\n- Extract component data from various formats (text, structured data, direct quantities)\\n- Handle multiple input sources: financial_data, components dict, quantity lists\\n- Apply intelligent data validation and sanitization\\n- Support weighted analysis for strategic component prioritization\\n\\nMCP Tool Integration:\\n- Use ShortageIndexTool for basic shortage calculations: shortage_index = 1 - min(available_qty[i] / required_qty[i])\\n- Use WeightedShortageIndexTool for strategic analysis: weighted_shortage = sum(weight[i] * shortage[i])\\n- Implement fallback calculations when MCP services unavailable\\n- Handle tool errors gracefully with comprehensive error recovery\\n\\nRisk Classification System:\\n- LOW Risk (0.0-0.3): Adequate inventory levels, minimal supply chain concerns\\n- MEDIUM Risk (0.3-0.7): Moderate shortages, requires monitoring and proactive measures\\n- HIGH Risk (0.7-1.0): Critical shortages, immediate action required\\n\\nAdvanced Output Generation:\\n- Provide detailed component-by-component analysis\\n- Include both simple and weighted shortage indices when applicable\\n- Generate risk-appropriate recommendations (urgent vs. preventive)\\n- Cite calculation methods and data sources for transparency\\n- Maintain professional tone with quantitative backing\\n\\nError Handling and Resilience:\\n- Gracefully handle MCP service unavailability with local calculations\\n- Validate and sanitize all input data before processing\\n- Provide meaningful error messages and fallback responses\\n- Log processing steps for debugging and improvement\\n\\nData Source Flexibility:\\n- Financial text parsing: \"CPU available is 1, require is 2, weight 0.2\"\\n- Structured components: {\"cpu\": {\"required\": 2, \"available\": 1}}\\n- Direct quantity lists: required_quantities=[2,4], available_quantities=[1,2]\\n- Mixed data sources with intelligent merging and validation\\n\\nQuality Assurance:\\n- Normalize weights to sum to 1.0 for weighted calculations\\n- Ensure non-negative quantities and valid ranges\\n- Cross-validate results between different calculation methods\\n- Provide confidence indicators for analysis quality\\n\\nAlways maintain analytical rigor, provide data-driven insights, and ensure actionable recommendations aligned with risk levels.'}, {'role': 'user', 'content': 'Extract all inventory data from the following MySQL agent response.\\nFor each material mentioned, identify:\\n- Material code (e.g., MM2004IC001, DEP2004IC002)\\n- Available quantity in stock\\n- Required quantity (if mentioned)\\n- Minimum stock level (if mentioned)\\n- Material description (if provided)\\n\\nMySQL Agent Response:\\nWorkflow Analysis: shortage_analysis (shortage_only)\\nOriginal Query: The order from a customer needs 10000 units of GPU MM2004IC001. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient.\\nProgress: Step 1/3\\n\\nMySQL Analysis Results:\\n- Response: Subject: CRITICAL DATABASE FAILURE - MATERIAL SHORTAGE ALERT\\nTo: Jim Xiao\\nBody: Unable to verify inventory for material MM2004IC001 (GPU) due to critical database access failure. New order requires 10000 units. Immediate IT intervention required.\\n- Structured Data:\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\nmysql_agent result: Error: unhandled errors in a TaskGroup (1 sub-exception)\\n\\nExtract all materials with their inventory data. Be precise with quantities and material codes.'}], 'max_tokens': 4096, 'temperature': 0.7, 'stop': None, 'tools': [{'type': 'function', 'function': {'name': 'fetch_fetch', 'description': 'Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.', 'parameters': {'description': 'Parameters for fetching a URL.', 'properties': {'url': {'description': 'URL to fetch', 'format': 'uri', 'minLength': 1, 'title': 'Url', 'type': 'string'}, 'max_length': {'default': 5000, 'description': 'Maximum number of characters to return.', 'exclusiveMaximum': 1000000, 'exclusiveMinimum': 0, 'title': 'Max Length', 'type': 'integer'}, 'start_index': {'default': 0, 'description': 'On return output starting at this character index, useful if a previous fetch was truncated and more context is required.', 'minimum': 0, 'title': 'Start Index', 'type': 'integer'}, 'raw': {'default': False, 'description': 'Get the actual HTML content of the requested page, without simplification.', 'title': 'Raw', 'type': 'boolean'}}, 'required': ['url'], 'title': 'Fetch', 'type': 'object'}}}], 'extra_body': {'chat_template_kwargs': {'enable_thinking': False}}}"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:15.858892","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Chat in progress","data":{"data":{"progress_action":"Chatting","model":"Qwen/Qwen3-32B","agent_name":"enhanced_shortage_analyzer_websocketorchestration","chat_turn":1}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184322","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Raw vLLM API response (asyncio mode):","data":{"data":{"id":"chatcmpl-0b19cb65d3204429bcd1c74a3e41f1d1","choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"Based on the MySQL agent response, I could only identify the following material information:\n\n- Material Code: MM2004IC001\n- Description: GPU\n- Required Quantity: 10,000 units\n- Available Quantity: Not available (database access failure)\n- Minimum Stock Level: Not mentioned\n\nThe response indicates a critical database failure preventing verification of the available inventory for MM2004IC001. No other materials were mentioned in the provided information.","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[],"reasoning_content":null},"stop_reason":null}],"created":1762388295,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":102,"prompt_tokens":1282,"total_tokens":1384,"completion_tokens_details":null,"prompt_tokens_details":null},"prompt_logprobs":null,"kv_transfer_params":null}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184594","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"vLLM ChatCompletion response:","data":{"data":{"id":"chatcmpl-0b19cb65d3204429bcd1c74a3e41f1d1","choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"Based on the MySQL agent response, I could only identify the following material information:\n\n- Material Code: MM2004IC001\n- Description: GPU\n- Required Quantity: 10,000 units\n- Available Quantity: Not available (database access failure)\n- Minimum Stock Level: Not mentioned\n\nThe response indicates a critical database failure preventing verification of the available inventory for MM2004IC001. No other materials were mentioned in the provided information.","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[]}}],"created":1762388295,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":102,"prompt_tokens":1282,"total_tokens":1384,"completion_tokens_details":null,"prompt_tokens_details":null},"_request_id":null}}}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184657","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Iteration 0: Stopping because finish_reason is 'stop'"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184717","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Chat finished","data":{"data":{"progress_action":"Finished","model":"Qwen/Qwen3-32B","agent_name":"enhanced_shortage_analyzer_websocketorchestration"}}}
{"level":"INFO","timestamp":"2025-11-06T00:18:18.184874","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using model from request_params: Qwen/Qwen3-32B"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184915","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"VLLMRequestStructuredCompletionRequest created: <class 'mcp_agent.workflows.llm.augmented_llm_vllm.VLLMRequestStructuredCompletionRequest'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184929","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Response model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184949","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Request object response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184959","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Request object response_model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184973","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Execution engine: asyncio"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.184987","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using asyncio direct execution path"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.190640","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"_execute_structured_completion_direct called with request type: <class 'mcp_agent.workflows.llm.augmented_llm_vllm.VLLMRequestStructuredCompletionRequest'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.190675","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Request response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.190689","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Request response_model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.190705","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Request serialized_response_model: None"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.190716","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Using direct response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.190728","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Response model type: <class 'pydantic._internal._model_construction.ModelMetaclass'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.190742","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Creating async instructor client with base_url: http://192.168.1.54:38701/v1"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.254591","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Instructor client created: <class 'instructor.core.client.AsyncInstructor'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:18:18.254620","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"About to call client.chat.completions.create with response_model: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:02.943494","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Structured response created successfully: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:02.943534","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Structured response type: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:02.943548","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Final structured response type: <class 'schemas.agent_schemas.InventoryExtractionSchema'>"}
{"level":"ERROR","timestamp":"2025-11-06T00:19:02.956382","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Error: Tool 'ShortageIndex' not found"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.956867","namespace":"orchestrator.workflow_patterns.tool_call_error","message":"Emitting shortage tool event via tool callback | type=%s"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:02.958522","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_a182a2cb.json"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.958567","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.958586","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 48.03s"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.958916","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.958960","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.958973","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.959780","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.963240","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-11-06T00:19:02.963433","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'message': 'No alerts ge...thin normal thresholds'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.12/v/list_type"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.963458","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.00s"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.963556","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_a182a2cb completed in 180.01s"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:02.964733","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_a182a2cb.json"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.964783","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_a182a2cb"}
{"level":"INFO","timestamp":"2025-11-06T00:19:02.964801","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_a182a2cb (success: True, time: 180.02s)"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:03.048436","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:03.048666","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Decremented connection ref count to 1"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:03.048685","namespace":"mcp_agent.mcp.mcp_aggregator.enhanced_shortage_analyzer_websocketorchestration","message":"Aggregator closing with ref count 1, connection manager will remain active"}
{"level":"DEBUG","timestamp":"2025-11-06T00:19:03.048725","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-11-06T00:19:03.048781","namespace":"mcp_agent.financial_websocket_orchestration_debug_user","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_debug_user","agent_name":"mcp_application_loop"}}}
