$schema: ../../../schema/mcp-agent.config.schema.json

execution_engine: asyncio

logger:
  transports: [console, file]
  level: debug
  show_progress: true
  path: "logs/linkedin-to-filesystem.jsonl"
  path_settings:
    path_pattern: "logs/linkedin-to-filesystem-{unique_id}.jsonl"
    unique_id: "timestamp"
    timestamp_format: "%Y%m%d_%H%M%S"

mcp:
  servers:
    playwright:
      command: "npx"
      args: ["@playwright/mcp@latest"]
      description: "Drive browser automation via Playwright"
    filesystem:
      command: "npx"
      args: [
          "-y",
          "@modelcontextprotocol/server-filesystem",
          "FILESYSTEM_PATH"]
      description: "Access Filesystem operations"
