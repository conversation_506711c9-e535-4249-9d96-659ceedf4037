$schema: ../../schema/mcp-agent.config.schema.json
# Configuration for Real Estate Analyzer with g-search-mcp
execution_engine: asyncio

# Logger configuration
logger:
  transports: [file]
  level: debug
  progress_display: true
  path_settings:
    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"
    unique_id: "timestamp"
    timestamp_format: "%Y%m%d_%H%M%S"

# MCP server configurations
mcp:
  servers:
    # Fetch server for basic web retrieval
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]
    
    # Google Search MCP server
    g-search:
      command: "npx"
      args: ["-y", "g-search-mcp"]
    
    # Filesystem server for writing reports
    filesystem:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-filesystem", "."]
    
    # RentSpider API server
    rentspider_api:
      command: "python3"
      args: ["rentspider_server.py"]  # Changed from simple_rentspider_test.py
      description: "RentSpider API server with elicitation"
      env:
        RENTSPIDER_API_KEY: "YOUR_API_KEY"

# Default OpenAI configuration
openai:
  default_model: gpt-4o