#!/usr/bin/env python3
"""
Test MySQL agent in pure synchronous context.
"""

import sys
import traceback
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_mysql_agent_sync():
    """Test the MySQL agent in pure synchronous context."""
    print("=== MySQL Agent Sync Test ===")
    
    try:
        # Import the MySQL agent module
        import agents.mysql_agent as mysql_module

        print("✓ Successfully imported MySQL agent components")

        # Check the current state of tools and schemas
        print(f"Initial tools: {mysql_module.tools}")
        print(f"Initial available_schemas: {mysql_module.available_schemas}")
        print(f"Initial ActionUnion: {mysql_module.ActionUnion}")

        # Force tool initialization
        print("Forcing tool initialization...")
        mysql_module._initialize_tools()
        print(f"After initialization - tools: {mysql_module.tools}")
        print(f"After initialization - available_schemas: {mysql_module.available_schemas}")
        print(f"After initialization - ActionUnion: {mysql_module.ActionUnion}")
        
        # Create the MySQL agent
        print("Creating MySQL orchestrator agent...")
        mysql_agent = mysql_module.create_mysql_orchestrator_agent()
        print("✓ MySQL agent created successfully")

        # Test input
        test_query = "Show me the tables in the database"
        mysql_input = mysql_module.MCPOrchestratorInputSchema(query=test_query)
        print(f"✓ Created test input: {test_query}")

        # Test the safe_orchestrator_run function directly
        print("Testing safe_orchestrator_run...")
        try:
            result = mysql_module.safe_orchestrator_run(mysql_agent, mysql_input)
            print(f"✓ safe_orchestrator_run completed successfully")
            print(f"  - Result type: {type(result)}")
            print(f"  - Action type: {type(result.action)}")
            print(f"  - Reasoning: {result.reasoning[:100]}..." if len(result.reasoning) > 100 else f"  - Reasoning: {result.reasoning}")

            if isinstance(result.action, mysql_module.FinalResponseSchema):
                print(f"  - Final response: {result.action.response_text[:200]}..." if len(result.action.response_text) > 200 else f"  - Final response: {result.action.response_text}")
            else:
                print(f"  - Action schema: {result.action}")

        except Exception as e:
            print(f"✗ safe_orchestrator_run failed: {e}")
            print(f"Error type: {type(e)}")
            traceback.print_exc()
            return False
            
        print("✓ MySQL agent sync test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ MySQL agent sync test failed: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    print("Starting MySQL agent sync test...\n")
    
    success = test_mysql_agent_sync()
    
    print(f"\n=== Test Summary ===")
    print(f"MySQL agent sync test: {'✓ PASS' if success else '✗ FAIL'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
