#!/usr/bin/env python3
"""
Test to understand when the MySQL agent is called with/without an event loop.
"""

import asyncio
import sys
import traceback
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_sync_context():
    """Test MySQL agent creation in synchronous context."""
    print("=== Testing MySQL Agent in Sync Context ===")
    
    try:
        # Check if there's an event loop
        try:
            loop = asyncio.get_running_loop()
            print(f"✗ Event loop detected in sync context: {loop}")
            return False
        except RuntimeError:
            print("✓ No event loop detected (as expected in sync context)")
        
        # Import and create MySQL agent
        from agents.mysql_agent import create_mysql_orchestrator_agent, tools, available_schemas
        
        print(f"Before creation - tools: {tools}")
        print(f"Before creation - available_schemas: {available_schemas}")
        
        mysql_agent = create_mysql_orchestrator_agent()
        
        print(f"After creation - tools: {tools}")
        print(f"After creation - available_schemas: {available_schemas}")
        
        print("✓ MySQL agent created successfully in sync context")
        return True
        
    except Exception as e:
        print(f"✗ MySQL agent creation failed in sync context: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

async def test_async_context():
    """Test MySQL agent creation in async context."""
    print("\n=== Testing MySQL Agent in Async Context ===")
    
    try:
        # Check if there's an event loop
        try:
            loop = asyncio.get_running_loop()
            print(f"✓ Event loop detected in async context: {loop}")
        except RuntimeError:
            print("✗ No event loop detected (unexpected in async context)")
            return False
        
        # Import and create MySQL agent
        from agents.mysql_agent import create_mysql_orchestrator_agent, tools, available_schemas
        
        print(f"Before creation - tools: {tools}")
        print(f"Before creation - available_schemas: {available_schemas}")
        
        mysql_agent = create_mysql_orchestrator_agent()
        
        print(f"After creation - tools: {tools}")
        print(f"After creation - available_schemas: {available_schemas}")
        
        print("✓ MySQL agent created successfully in async context")
        return True
        
    except Exception as e:
        print(f"✗ MySQL agent creation failed in async context: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

def test_direct_initialization():
    """Test direct tool initialization."""
    print("\n=== Testing Direct Tool Initialization ===")
    
    try:
        from agents.mysql_agent import _initialize_tools, tools, available_schemas
        
        print(f"Before initialization - tools: {tools}")
        print(f"Before initialization - available_schemas: {available_schemas}")
        
        # Check event loop context
        try:
            loop = asyncio.get_running_loop()
            print(f"Event loop detected: {loop}")
        except RuntimeError:
            print("No event loop detected")
        
        # Call initialization
        _initialize_tools()
        
        print(f"After initialization - tools: {tools}")
        print(f"After initialization - available_schemas: {available_schemas}")
        
        print("✓ Direct tool initialization completed")
        return True
        
    except Exception as e:
        print(f"✗ Direct tool initialization failed: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

async def test_direct_initialization_async():
    """Test direct tool initialization in async context."""
    print("\n=== Testing Direct Tool Initialization in Async Context ===")
    
    try:
        from agents.mysql_agent import _initialize_tools, tools, available_schemas
        
        print(f"Before initialization - tools: {tools}")
        print(f"Before initialization - available_schemas: {available_schemas}")
        
        # Check event loop context
        try:
            loop = asyncio.get_running_loop()
            print(f"Event loop detected: {loop}")
        except RuntimeError:
            print("No event loop detected")
        
        # Call initialization
        _initialize_tools()
        
        print(f"After initialization - tools: {tools}")
        print(f"After initialization - available_schemas: {available_schemas}")
        
        print("✓ Direct tool initialization completed in async context")
        return True
        
    except Exception as e:
        print(f"✗ Direct tool initialization failed in async context: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("Starting event loop context tests...\n")
    
    # Test 1: Sync context
    sync_success = test_sync_context()
    
    # Test 2: Async context
    async_success = await test_async_context()
    
    # Test 3: Direct initialization in sync context
    direct_sync_success = test_direct_initialization()
    
    # Test 4: Direct initialization in async context
    direct_async_success = await test_direct_initialization_async()
    
    # Summary
    print("\n=== Test Summary ===")
    print(f"Sync context test: {'✓ PASS' if sync_success else '✗ FAIL'}")
    print(f"Async context test: {'✓ PASS' if async_success else '✗ FAIL'}")
    print(f"Direct sync initialization: {'✓ PASS' if direct_sync_success else '✗ FAIL'}")
    print(f"Direct async initialization: {'✓ PASS' if direct_async_success else '✗ FAIL'}")
    
    return 0

if __name__ == "__main__":
    # First test sync context
    print("=== SYNC CONTEXT TESTS ===")
    test_sync_context()
    test_direct_initialization()
    
    print("\n=== ASYNC CONTEXT TESTS ===")
    # Then test async context
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
