#!/usr/bin/env python3
"""
Simple test to check global variable behavior.
"""

import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_globals():
    """Test global variable behavior."""
    print("=== Testing Global Variables ===")
    
    # Import the module
    import agents.mysql_agent as mysql_module
    
    print(f"Initial state:")
    print(f"  tools: {mysql_module.tools}")
    print(f"  available_schemas: {mysql_module.available_schemas}")
    print(f"  ActionUnion: {mysql_module.ActionUnion}")
    
    # Call initialization
    print(f"\nCalling _initialize_tools()...")
    mysql_module._initialize_tools()
    
    print(f"\nAfter _initialize_tools():")
    print(f"  tools: {mysql_module.tools}")
    print(f"  available_schemas: {mysql_module.available_schemas}")
    print(f"  ActionUnion: {mysql_module.ActionUnion}")
    
    # Check if the module variables are the same as the global variables
    print(f"\nChecking module variables vs globals:")
    print(f"  mysql_module.tools is mysql_module.tools: {mysql_module.tools is mysql_module.tools}")
    print(f"  id(mysql_module.tools): {id(mysql_module.tools)}")
    print(f"  id(mysql_module.available_schemas): {id(mysql_module.available_schemas)}")
    print(f"  id(mysql_module.ActionUnion): {id(mysql_module.ActionUnion)}")
    
    # Try to access the variables directly
    print(f"\nDirect access to module globals:")
    module_globals = vars(mysql_module)
    print(f"  'tools' in module_globals: {'tools' in module_globals}")
    print(f"  module_globals['tools']: {module_globals.get('tools', 'NOT FOUND')}")
    print(f"  module_globals['available_schemas']: {module_globals.get('available_schemas', 'NOT FOUND')}")
    print(f"  module_globals['ActionUnion']: {module_globals.get('ActionUnion', 'NOT FOUND')}")
    
    return True

def main():
    """Run the test."""
    print("Starting globals test...\n")
    
    success = test_globals()
    
    print(f"\n=== Test Summary ===")
    print(f"Globals test: {'✓ PASS' if success else '✗ FAIL'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
