#!/usr/bin/env python3
"""
Direct test of MySQL agent to isolate the schema validation issue.
This script tests the MySQL agent directly without the orchestration layer.
"""

import asyncio
import sys
import os
import traceback
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_mysql_agent_direct():
    """Test the MySQL agent directly to isolate the schema validation issue."""
    print("=== Direct MySQL Agent Test ===")

    try:
        # Import the MySQL agent components
        from agents.mysql_agent import (
            create_mysql_orchestrator_agent,
            MCPOrchestratorInputSchema,
            safe_orchestrator_run,
            FinalResponseSchema,
            tools,
            available_schemas,
            ActionUnion,
            _initialize_tools
        )

        print("✓ Successfully imported MySQL agent components")

        # Check the current state of tools and schemas
        print(f"Current tools: {tools}")
        print(f"Current available_schemas: {available_schemas}")
        print(f"Current ActionUnion: {ActionUnion}")

        # Force tool initialization
        print("Forcing tool initialization...")
        _initialize_tools()
        print(f"After initialization - tools: {tools}")
        print(f"After initialization - available_schemas: {available_schemas}")
        print(f"After initialization - ActionUnion: {ActionUnion}")

        # Create the MySQL agent
        print("Creating MySQL orchestrator agent...")
        mysql_agent = create_mysql_orchestrator_agent()
        print("✓ MySQL agent created successfully")

        # Test input
        test_query = "Show me the tables in the database"
        mysql_input = MCPOrchestratorInputSchema(query=test_query)
        print(f"✓ Created test input: {test_query}")

        # Test the safe_orchestrator_run function directly
        print("Testing safe_orchestrator_run...")
        try:
            result = safe_orchestrator_run(mysql_agent, mysql_input)
            print(f"✓ safe_orchestrator_run completed successfully")
            print(f"  - Result type: {type(result)}")
            print(f"  - Action type: {type(result.action)}")
            print(f"  - Reasoning: {result.reasoning[:100]}..." if len(result.reasoning) > 100 else f"  - Reasoning: {result.reasoning}")

            if isinstance(result.action, FinalResponseSchema):
                print(f"  - Final response: {result.action.response_text[:100]}..." if len(result.action.response_text) > 100 else f"  - Final response: {result.action.response_text}")
            else:
                print(f"  - Action schema: {result.action}")

        except Exception as e:
            print(f"✗ safe_orchestrator_run failed: {e}")
            print(f"Error type: {type(e)}")
            traceback.print_exc()
            return False

        print("✓ Direct MySQL agent test completed successfully")
        return True

    except Exception as e:
        print(f"✗ Direct MySQL agent test failed: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

async def test_mysql_agent_interface():
    """Test the MySQL agent through the interface layer."""
    print("\n=== MySQL Agent Interface Test ===")
    
    try:
        # Import the interface components
        from agents.agent_interfaces import (
            MySQLAgentInterface,
            AgentInputSchema,
            AgentExecutionContext
        )
        from agents.mysql_agent import create_mysql_orchestrator_agent
        
        print("✓ Successfully imported interface components")
        
        # Create the MySQL agent and interface
        mysql_agent = create_mysql_orchestrator_agent()
        mysql_interface = MySQLAgentInterface(mysql_agent)
        print("✓ MySQL agent interface created successfully")
        
        # Initialize the interface
        await mysql_interface.initialize_for_orchestration()
        print("✓ MySQL agent interface initialized")
        
        # Create test input
        test_query = "Show me the tables in the database"
        context = AgentExecutionContext(shared_context="Testing MySQL agent interface")
        agent_input = AgentInputSchema(query=test_query, context=context)
        print(f"✓ Created test input: {test_query}")
        
        # Test the interface execution
        print("Testing interface execution...")
        try:
            result = await mysql_interface.execute_orchestrated(agent_input)
            print(f"✓ Interface execution completed")
            print(f"  - Success: {result.success}")
            print(f"  - Execution time: {result.execution_time:.3f}s")
            if result.success:
                print(f"  - Result: {str(result.result)[:200]}..." if len(str(result.result)) > 200 else f"  - Result: {result.result}")
            else:
                print(f"  - Error: {result.error}")
                
        except Exception as e:
            print(f"✗ Interface execution failed: {e}")
            print(f"Error type: {type(e)}")
            traceback.print_exc()
            return False
            
        print("✓ MySQL agent interface test completed")
        return True
        
    except Exception as e:
        print(f"✗ MySQL agent interface test failed: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("Starting MySQL agent debugging tests...\n")
    
    # Test 1: Direct MySQL agent
    direct_success = await test_mysql_agent_direct()
    
    # Test 2: MySQL agent interface
    interface_success = await test_mysql_agent_interface()
    
    # Summary
    print("\n=== Test Summary ===")
    print(f"Direct MySQL agent test: {'✓ PASS' if direct_success else '✗ FAIL'}")
    print(f"MySQL agent interface test: {'✓ PASS' if interface_success else '✗ FAIL'}")
    
    if direct_success and interface_success:
        print("✓ All tests passed - MySQL agent is working correctly")
        return 0
    else:
        print("✗ Some tests failed - MySQL agent has issues")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
