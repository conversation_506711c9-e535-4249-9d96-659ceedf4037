#!/usr/bin/env python3
"""
Test to check ATOMIC_COMPONENTS_AVAILABLE flag.
"""

import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_atomic_components():
    """Test the ATOMIC_COMPONENTS_AVAILABLE flag."""
    print("=== Testing ATOMIC_COMPONENTS_AVAILABLE ===")
    
    try:
        from agents.mysql_agent import ATOMIC_COMPONENTS_AVAILABLE, tools
        
        print(f"ATOMIC_COMPONENTS_AVAILABLE: {ATOMIC_COMPONENTS_AVAILABLE}")
        print(f"tools: {tools}")
        print(f"tools is falsy: {not tools}")
        print(f"Condition (tools or not ATOMIC_COMPONENTS_AVAILABLE): {tools or not ATOMIC_COMPONENTS_AVAILABLE}")
        
        # Test individual imports
        try:
            from atomic_agents.lib.factories.mcp_tool_factory import fetch_mcp_tools
            print("✓ fetch_mcp_tools import successful")
        except Exception as e:
            print(f"✗ fetch_mcp_tools import failed: {e}")
            
        try:
            from atomic_agents.agents.base_agent import BaseAgent, BaseAgentConfig, BaseIOSchema
            print("✓ BaseAgent imports successful")
        except Exception as e:
            print(f"✗ BaseAgent imports failed: {e}")
            
        try:
            from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator
            print("✓ SystemPromptGenerator import successful")
        except Exception as e:
            print(f"✗ SystemPromptGenerator import failed: {e}")
            
        try:
            from atomic_agents.lib.components.agent_memory import AgentMemory
            print("✓ AgentMemory import successful")
        except Exception as e:
            print(f"✗ AgentMemory import failed: {e}")
            
        try:
            import instructor
            print("✓ instructor import successful")
        except Exception as e:
            print(f"✗ instructor import failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def main():
    """Run the test."""
    print("Starting atomic components test...\n")
    
    success = test_atomic_components()
    
    print(f"\n=== Test Summary ===")
    print(f"Atomic components test: {'✓ PASS' if success else '✗ FAIL'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
