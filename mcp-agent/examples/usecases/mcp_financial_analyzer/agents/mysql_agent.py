"""
MySQL Agent for historical financial data analysis integration with MySQL MCP server.
Follows the reference pattern from dev_agent/db_agent_develop/MySQL/client.py for MCP orchestration.
"""

import os
import json
import logging
import asyncio
import inspect
import time
import concurrent.futures
from datetime import datetime
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional, Tuple, Union as TypingUnion, Literal

# Configure logging
logger = logging.getLogger("mysql_analyzer")

try:
    # Import atomic-agents components (matches reference pattern)
    from atomic_agents.lib.factories.mcp_tool_factory import fetch_mcp_tools
    from atomic_agents.agents.base_agent import BaseAgent, BaseAgentConfig, BaseIOSchema
    from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator
    from atomic_agents.lib.components.agent_memory import AgentMemory
    import instructor
    from openai import OpenAI
    from pydantic import Field
    ATOMIC_COMPONENTS_AVAILABLE = True
except Exception as e:
    logger.warning(f"Failed to import atomic-agents components: {e}")
    ATOMIC_COMPONENTS_AVAILABLE = False
    fetch_mcp_tools = None
    BaseAgent = None
    BaseAgentConfig = None
    BaseIOSchema = None
    SystemPromptGenerator = None
    AgentMemory = None
    instructor = None
    openai = None
    OpenAI = None


@dataclass
class MCPConfig:
    """Configuration for the MCP Orchestrator (SSE transport)."""

    mcp_server_url: str = os.getenv("MYSQL_MCP_SERVER_URL", "http://localhost:8702")
    vllm_api_base: str = os.getenv("VLLM_API_BASE", "http://************:38701/v1")
    vllm_model: str = os.getenv("VLLM_MODEL", "Qwen/Qwen3-32B")
    vllm_api_key: str = os.getenv("VLLM_API_KEY", "EMPTY")


config = MCPConfig()

# Build vLLM client via instructor (updated to use vLLM instead of OpenAI)
client = None
if ATOMIC_COMPONENTS_AVAILABLE:
    try:
        client = instructor.from_openai(
            OpenAI(
                base_url=config.vllm_api_base,
                api_key=config.vllm_api_key
            )
        )
        logger.info(f"✓ vLLM client initialized: {config.vllm_api_base} with model {config.vllm_model}")
    except Exception as _e:
        logger.warning(f"Failed to initialize vLLM client for orchestrator: {_e}")
        client = None


# Final response schema (matches reference)
class FinalResponseSchema(BaseIOSchema):
    """Schema for providing a final text response to the user."""

    response_text: str = Field(..., description="The final text response to the user's query")


# Global variables for lazy initialization
tools: List[type] = []
tool_schema_to_class_map: Dict[Any, Any] = {}
tool_input_schemas: Tuple[Any, ...] = tuple()
available_schemas: Tuple[Any, ...] = (FinalResponseSchema,)
ActionUnion = TypingUnion[FinalResponseSchema]


def _set_action_union(schema_tuple: Tuple[Any, ...]) -> None:
    """Update the ActionUnion to reflect the currently available schemas."""
    global ActionUnion
    if not schema_tuple:
        ActionUnion = FinalResponseSchema
    elif len(schema_tuple) == 1:
        ActionUnion = schema_tuple[0]
    else:
        ActionUnion = TypingUnion[tuple(schema_tuple)]


def _initialize_tools():
    """Lazy initialization of MCP tools to avoid hanging during import."""
    global tools, tool_schema_to_class_map, tool_input_schemas, available_schemas, ActionUnion

    if tools or not ATOMIC_COMPONENTS_AVAILABLE:
        return  # Already initialized or not available

    if fetch_mcp_tools is None:
        logger.warning("fetch_mcp_tools unavailable; falling back to custom schema.")
        _initialize_tools_sync_fallback()
        return

    def _fetch_tools_blocking():
        return fetch_mcp_tools(
            mcp_endpoint=config.mcp_server_url,
            use_stdio=False,
        )

    try:
        loop_running = False
        try:
            asyncio.get_running_loop()
            loop_running = True
        except RuntimeError:
            loop_running = False

        if loop_running:
            logger.info("Detected running event loop, fetching MCP tool definitions in background thread...")
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(_fetch_tools_blocking)
                fetched_tools = future.result()
        else:
            fetched_tools = _fetch_tools_blocking()

        globals()["tools"] = list(fetched_tools or [])

        if not tools:
            logger.warning("fetch_mcp_tools returned no tools; using custom fallback schema.")
            _initialize_tools_sync_fallback()
            return

        _process_fetched_tools()

    except Exception as e:
        logger.warning(f"Failed to fetch MCP tools: {e}")
        globals()["tools"] = []
        _initialize_tools_sync_fallback()

def _initialize_tools_sync_fallback():
    """Fallback initialization when in async context - creates a custom MCP tool."""
    global tools, tool_schema_to_class_map, tool_input_schemas, available_schemas, ActionUnion

    logger.info("Creating custom MCP tool for async context...")

    try:
        # Create a custom tool class that handles MCP calls directly
        from pydantic import Field

        class MySQLFallbackInputData(BaseIOSchema):
            """Fallback payload for executing a mysql_agent query."""

            query: str = Field(..., description="Raw SQL query to execute via the mysql_agent MCP tool.")

        class MySQLAgentInputSchema(BaseIOSchema):
            """Fallback wrapper schema mirroring the generated mysql_agent input."""

            input_data: MySQLFallbackInputData = Field(
                ..., description="Structured arguments passed to the mysql_agent tool."
            )
            tool_name: Literal["mysql_agent"] = Field(
                ..., description="Literal identifier required for mysql_agent tool calls."
            )

        class MySQLAgentOutputSchema(BaseIOSchema):
            """Fallback output schema for mysql_agent results."""

            result: str = Field(..., description="Serialized result returned by the mysql_agent MCP tool.")

        class CustomMySQLTool:
            """Custom MySQL tool that handles async MCP calls."""
            input_schema = MySQLAgentInputSchema
            output_schema = MySQLAgentOutputSchema
            mcp_tool_name = "mysql_agent"

            def run(self, input_data):
                """Execute the MySQL query via direct MCP call."""
                try:
                    # Import MCP client components
                    from mcp.client.sse import sse_client
                    from mcp import ClientSession
                    import asyncio

                    async def execute_query():
                        sse_endpoint = f"{config.mcp_server_url}/sse"
                        async with sse_client(sse_endpoint) as (read_stream, write_stream):
                            async with ClientSession(read_stream, write_stream) as session:
                                await session.initialize()
                                result = await session.call_tool(
                                    name="mysql_agent",
                                    arguments={"input_data": {"query": input_data.input_data.query}},
                                )
                                return result

                    # Check if we're in an async context
                    try:
                        asyncio.get_running_loop()
                        # We're in an async context, create a task
                        import concurrent.futures
                        with concurrent.futures.ThreadPoolExecutor() as executor:
                            future = executor.submit(asyncio.run, execute_query())
                            result = future.result()
                    except RuntimeError:
                        # No running loop, safe to use asyncio.run
                        result = asyncio.run(execute_query())

                    # Process the result - handle TextContent objects
                    if hasattr(result, 'content'):
                        content = result.content
                        # If content is a list of TextContent objects, extract text
                        if isinstance(content, list):
                            text_parts = []
                            for item in content:
                                if hasattr(item, 'text'):
                                    text_parts.append(item.text)
                                else:
                                    text_parts.append(str(item))
                            content = '\n'.join(text_parts)
                    elif isinstance(result, dict) and 'content' in result:
                        content = result['content']
                        # Handle list of TextContent in dict format
                        if isinstance(content, list):
                            text_parts = []
                            for item in content:
                                if isinstance(item, dict) and 'text' in item:
                                    text_parts.append(item['text'])
                                elif hasattr(item, 'text'):
                                    text_parts.append(item.text)
                                else:
                                    text_parts.append(str(item))
                            content = '\n'.join(text_parts)
                    else:
                        content = str(result)

                    return MySQLAgentOutputSchema(result=content)

                except Exception as e:
                    logger.error(f"Error executing MySQL query: {e}")
                    return MySQLAgentOutputSchema(result=f"Error: {str(e)}")

        # Set up the tool mapping (update global variables)
        globals()['tools'] = [CustomMySQLTool]
        globals()['tool_schema_to_class_map'] = {MySQLAgentInputSchema: CustomMySQLTool}
        globals()['tool_input_schemas'] = (MySQLAgentInputSchema,)
        globals()['available_schemas'] = globals()['tool_input_schemas'] + (FinalResponseSchema,)
        _set_action_union(globals()['available_schemas'])

        logger.info("Custom MySQL tool created successfully")

    except Exception as e:
        logger.warning(f"Failed to create custom MySQL tool: {e}")
        # Create minimal fallback (update global variables)
        globals()['tools'] = []
        globals()['tool_schema_to_class_map'] = {}
        globals()['tool_input_schemas'] = tuple()
        globals()['available_schemas'] = (FinalResponseSchema,)
        _set_action_union(globals()['available_schemas'])

def _process_fetched_tools():
    """Process the fetched tools and set up schemas."""
    global tools, tool_schema_to_class_map, tool_input_schemas, available_schemas, ActionUnion

    if not tools:
        logger.warning("No MCP tools found. Please ensure the MySQL MCP server is running and accessible.")
        return

    # Build mapping from input_schema to ToolClass (matches reference)
    tool_schema_to_class_map = {
        ToolClass.input_schema: ToolClass for ToolClass in tools if hasattr(ToolClass, "input_schema")
    }

    # Collect all tool input schemas (matches reference)
    tool_input_schemas = tuple(tool_schema_to_class_map.keys())
    logger.info(f"Tool input schemas: {[schema.__name__ for schema in tool_input_schemas]}")

    # Available schemas include all tool input schemas and the final response schema (matches reference)
    available_schemas = tool_input_schemas + (FinalResponseSchema,)
    logger.info(f"Available schemas: {[schema.__name__ for schema in available_schemas]}")

    # Define the Union of all action schemas (matches reference)
    _set_action_union(available_schemas)


def safe_orchestrator_run(agent, *args, **kwargs):
    """Run orchestrator with retries and inline validation feedback (matches reference)."""
    # Initialize tools lazily
    _initialize_tools()

    max_retry = 20
    example_schema = {
        "reasoning": "To list all tables, I will use SHOW TABLES.",
        "action": {
            "tool_name": "mysql_agent",
            "input_data": {
                "query": "SHOW TABLES;"
            }
        }
    }
    for _ in range(max_retry):
        try:
            output = agent.run(*args, **kwargs)
            if hasattr(output, "action") and output.action is not None:
                return output
            else:
                logger.warning("Orchestrator validation failed: action field missing; injecting correction prompt.")
                agent.memory.add_message(
                    "system",
                    {"query": (
                        "【格式錯誤提醒】Your last output missed the 'action' field!\n"
                        "你的 output 必須同時包含 'reasoning'（推理過程）和 'action'（執行動作），action 必須是 tool input schema 或 FinalResponseSchema。\n"
                        "請嚴格按照下方 JSON 格式產生 output：\n"
                        f"{example_schema}\n"
                        "If you see a validation error, immediately retry with the correct action format.\n"
                    )},
                )
        except Exception as e:
            logger.error("Orchestrator validation error encountered during run attempt: %s", e, exc_info=True)
            agent.memory.add_message(
                "system",
                {"query": (
                    "【Validation Error】Your previous output caused a validation error!\n"
                    "錯誤訊息如下（請他媽參考並立即修正 output 格式）：\n"
                    f"{str(e)}\n"
                    "正確格式請參考範例：\n"
                    f"{example_schema}\n"
                )},
            )
    logger.error("LLM failed to output correct schema after %s retries; aborting orchestrator run.", max_retry)
    raise RuntimeError("LLM failed to output correct schema after fallback.")


# Schema definitions (matches reference)
class MCPOrchestratorInputSchema(BaseIOSchema):
    """Input schema for the MCP Orchestrator Agent."""

    query: str = Field(..., description="The user's query to analyze.")


def create_orchestrator_output_schema():
    """Create the output schema dynamically after tools are initialized."""
    global ActionUnion

    class OrchestratorOutputSchema(BaseIOSchema):
        """Output schema for the orchestrator. Contains reasoning and the chosen action."""

        reasoning: str = Field(
            ..., description="Detailed explanation of why this action was chosen and how it will address the user's query."
        )
        action: ActionUnion = Field(  # type: ignore[reportInvalidTypeForm]
            ..., description="The chosen action: either a tool's input schema instance or a final response schema instance."
        )

        @classmethod
        def from_streaming_response(cls, payload: Dict[str, Any]) -> "OrchestratorOutputSchema":
            """Reconstruct orchestrator output from streaming payloads."""
            return cls(**payload)

    return OrchestratorOutputSchema

# Main logic and script entry point (matches reference pattern)
def create_mysql_orchestrator_agent(
    company_name: Optional[str] = None,
    tool_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
):
    """
    Create a MySQL MCP Orchestrator Agent following the reference pattern.

    Returns:
        BaseAgent configured for MySQL material shortage analysis
    """
    if not ATOMIC_COMPONENTS_AVAILABLE:
        raise RuntimeError("atomic-agents components not available. Please install atomic-agents.")

    # Initialize tools lazily
    _initialize_tools()

    # Create the output schema after tools are initialized
    OrchestratorOutputSchema = create_orchestrator_output_schema()

    try:
        logger.info("Initializing MySQL MCP Agent System (SSE mode)...")
        if company_name:
            logger.info("MySQL agent context company: %s", company_name)

        # Create and initialize orchestrator agent (matches reference)
        logger.info("Creating orchestrator agent...")
        memory = AgentMemory()
        orchestrator_agent = BaseAgent(
            BaseAgentConfig(
                client=client,
                model=config.vllm_model,
                memory=memory,
                system_prompt_generator=SystemPromptGenerator(
                    background=[
                        "You are a MySQL MCP Orchestrator Agent, designed to chat with users and",
                        "determine the best way to handle their queries using the available MySQL tools.",
                        "You can interact with the mysql_agent tool to execute SQL queries for material shortage analysis.",
                        "Please check the table's names and their schemas before executing any queries.",
                        """
                        When you call the `mysql_agent` tool, you MUST provide the input as a dictionary with the exact format:
                        {'input_data': {'query': '<YOUR_SQL_QUERY>'}, 'tool_name': 'mysql_agent'}

                        - Only the key 'query' is allowed under 'input_data'.
                        - Do NOT use other keys such as 'sql', 'table', or plain strings.
                        - All SQL statements (e.g., SHOW TABLES, DESCRIBE, SELECT...) must be placed in the value of 'query'.
                        """,
                        """
                        When returning a reasoning step, you must always include an 'action' field.
                        The 'action' field should be a tool call (e.g., mysql_agent tool call) or a FinalResponseSchema if you are providing the final answer.
                        Make sure to always include both 'reasoning' and 'action' fields in your output.

                        Example:
                        {
                            "reasoning": "To determine the columns available in CUSTOMER_ORDERS, I will use the DESCRIBE statement.",
                            "action": {
                                "tool_name": "mysql_agent",
                                "input_data": {
                                    "query": "DESCRIBE CUSTOMER_ORDERS;"
                                }
                            }
                        }
                        """,
                        "You can handle material shortage (out-of-stock) analysis for new orders.",
                        "When a user asks if a new order will cause material shortage:",
                        "1. Check the current stock (inventory) for each material required by the order.",
                        "2. If any material's current stock is less than the order's required quantity, a shortage has occurred.",
                        "3. When a shortage is detected, prepare an email including:",
                        "   - material_name",
                        "   - material_code",
                        "   - current_stock",
                        "   - required_quantity for the new order.",
                        "4. If the responsible staff is unknown, always send the email to Jacky.",
                        "5. When sending an email, only output the email content.",
                        "6. Please remember to email the staff if there is a shortage, and confirm to the user if all materials are sufficient. So that they can take action.",
                        "You specialize in historical supplier data analysis and material availability tracking.",
                        "For queries about material codes (e.g., DGRA00748) or order numbers (e.g., CUSTORD-202506001), use appropriate SQL queries to find:",
                        "- Supplier reliability and delivery performance",
                        "- Material availability and stock levels",
                        "- Order history and shipping addresses",
                        "- Historical shortage patterns for risk assessment",
                    ],
                    steps=[
                        "1. Use the reasoning field to determine if one or more successive tool calls could be used to handle the user's query.",
                        "2. If so, choose the appropriate tool(s) one at a time and extract all necessary parameters from the query.",
                        "2a. You MUST execute at least one mysql_agent tool call to retrieve live database data before giving a final response. Never rely on assumptions or default values.",
                        "3. If a single tool can not be used to handle the user's query, think about how to break down the query into "
                        "smaller tasks and route them to the appropriate tool(s).",
                        "4. If no sequence of tools could be used, or if you are finished processing the user's query, provide a final "
                        "response to the user.",
                        "5. Receive the user query about potential material shortage for a new order.",
                        "6. Look up required materials and their current stock.",
                        "7. Compare current_stock with the order's required_quantity for each material.",
                        "8. If current_stock < required_quantity, it's a shortage.",
                        "9. For any shortage, prepare and output the email content to notify the staff (to Jacky if unsure).",
                        "10. If no shortage, confirm to the user that all materials are sufficient.",
                        "11. For historical analysis queries, use appropriate SQL to find supplier reliability and material availability.",
                        "12. For order lookup queries, find shipping addresses and delivery information.",
                    ],
                    output_instructions=[
                        "Every output MUST have both 'reasoning' and 'action' fields, or it will result in a validation error.",
                        "Never omit the 'action' field; if you're done, use FinalResponseSchema.",
                        "1. Always provide a detailed explanation of your decision-making process in the 'reasoning' field.",
                        "2. Choose exactly one action schema (either a tool input or FinalResponseSchema).",
                        "3. Ensure all required parameters for the chosen tool are properly extracted and validated.",
                        "4. Maintain a professional and helpful tone in all responses.",
                        "5. Break down complex queries into sequential tool calls before giving the final answer via `FinalResponseSchema`.",
                        "6. Each round, you can only execute one tool call or give a final answer. Do not perform multiple steps at once.",
                        "7. If unsure about table or column names, always use list_tables or describe_table tools first, then construct the next query based on the results.",
                        "8. Never execute a SELECT SQL by guessing; always confirm the schema first.",
                        "9. If you receive an SQL error (e.g., 'table doesn't exist' or 'unknown column'), use list_tables or describe_table to find the correct table or column.",
                        "10. You must only send valid MySQL SQL queries (such as SHOW TABLES;, DESCRIBE tablename;) when using the mysql_agent tool. Do not use tool names as SQL commands.",
                        "10a. Do not produce a FinalResponseSchema until you have executed the necessary mysql_agent queries and incorporated their concrete results (e.g., required and current stock values) into your reasoning.",
                        "11. Your output MUST be a JSON object with both a 'reasoning' and 'action' field.",
                        "12. The 'action' field must be a tool call (e.g., mysql_agent) with all required parameters, or a FinalResponseSchema if you are answering the user directly.",
                        "13. Never omit the 'action' field, even for intermediate steps such as listing tables or describing a table.",
                        "14. Example: {'reasoning': 'To list all tables...', 'action': {'tool_name': 'mysql_agent', 'input_data': {'query': 'SHOW TABLES;'}}}",
                        "15. If you receive a validation error, immediately retry with the correct action format.",
                        "When a shortage occurs, only output the email notification content. Example:",
                        "Subject: Material Shortage Alert",
                        "To: Jacky",
                        "Body: Material [material_name] ([material_code]) is short for the new order. Current stock: [current_stock]. Required: [required_quantity]. Please arrange replenishment.",
                        "If no shortage, reply to user with normal action.",
                    ],
                ),
                input_schema=MCPOrchestratorInputSchema,
                output_schema=OrchestratorOutputSchema,
            )
        )
        if tool_stream_callback:
            setattr(orchestrator_agent, "tool_stream_callback", tool_stream_callback)
            logger.info("Attached initial tool_stream_callback to orchestrator agent")

        logger.info("Successfully created MySQL orchestrator agent.")

        def _now() -> float:
            """Return monotonic timestamp even outside event loop."""
            try:
                return asyncio.get_running_loop().time()
            except RuntimeError:
                return time.time()

        stream_chunk_index = 0
        stream_total_chars = 0

        def _timestamp() -> str:
            return datetime.utcnow().isoformat() + "Z"

        async def _maybe_emit_stream(text: str, callback: Optional[Callable[[Any], Any]]) -> None:
            if not callback:
                return
            try:
                nonlocal stream_chunk_index, stream_total_chars
                safe_text = text if isinstance(text, str) else str(text)
                stripped = safe_text.strip()

                event_type = "llm_response"
                extra: Dict[str, Any] = {}
                lower_stripped = stripped.lower()
                if lower_stripped.startswith("error"):
                    event_type = "error"
                    extra["severity"] = "error"

                payload: Dict[str, Any] = {
                    "type": event_type,
                    "label": "LLM response",
                    "content": safe_text,
                    "timestamp": _timestamp(),
                    "source": "mysql_orchestrator",
                }

                if event_type == "llm_response":
                    stream_chunk_index += 1
                    chunk_length = len(safe_text)
                    stream_total_chars += chunk_length
                    payload["chunk_index"] = stream_chunk_index
                    payload["metrics"] = {
                        "chunk_chars": chunk_length,
                        "cumulative_chars": stream_total_chars,
                    }

                if extra:
                    payload.update(extra)

                result = callback(payload)
                if inspect.isawaitable(result):
                    await result
            except Exception as stream_error:  # pragma: no cover - logging only
                logger.debug(f"Stream callback failed: {stream_error}")

        async def _maybe_emit_tool_event(event: Dict[str, Any]) -> None:
            tool_callback = getattr(orchestrator_agent, "tool_stream_callback", None)
            if not tool_callback:
                return
            try:
                result = tool_callback(event)
                if inspect.isawaitable(result):
                    await result
            except Exception as callback_error:  # pragma: no cover - logging only
                logger.debug(f"Tool stream callback failed: {callback_error}")

        def _normalize_tool_result(raw_result: Any) -> Any:
            if raw_result is None:
                return None
            if isinstance(raw_result, (list, dict)):
                return raw_result
            if hasattr(raw_result, "model_dump"):
                try:
                    return raw_result.model_dump()
                except Exception:  # pragma: no cover - defensive
                    return str(raw_result)
            if hasattr(raw_result, "dict"):
                try:
                    return raw_result.dict()  # type: ignore[attr-defined]
                except Exception:  # pragma: no cover
                    return str(raw_result)
            if isinstance(raw_result, str):
                trimmed = raw_result.strip()
                if not trimmed:
                    return ""
                try:
                    parsed = json.loads(trimmed)
                    return parsed
                except Exception:
                    return raw_result
            return raw_result

        async def _run_orchestrator(query: str) -> Any:
            return await asyncio.to_thread(
                safe_orchestrator_run,
                orchestrator_agent,
                MCPOrchestratorInputSchema(query=query),
            )

        async def _run_orchestrator_next() -> Any:
            return await asyncio.to_thread(safe_orchestrator_run, orchestrator_agent)

        async def process_query(
            query: str,
            stream_callback: Optional[Callable[[Any], Any]] = None,
        ) -> Dict[str, Any]:
            """Execute a MySQL analysis query with optional streaming callbacks."""
            await _maybe_emit_stream(
                f"MySQL orchestrator preparing analysis for query:\n{query}\n\n",
                stream_callback,
            )

            reasoning_history: List[str] = []
            tool_outputs: List[Dict[str, Any]] = []
            final_response: Optional[str] = None
            error_message: Optional[str] = None

            try:
                orchestrator_output = await _run_orchestrator(query)
            except Exception as exc:
                error_message = str(exc)
                await _maybe_emit_stream(f"Failed to start MySQL analysis: {error_message}\n", stream_callback)
                await _maybe_emit_tool_event(
                    {
                        "type": "tool_call_error",
                        "tool_name": "mysql_orchestrator",
                        "error": error_message,
                        "timestamp": _now(),
                    }
                )
                return {
                    "success": False,
                    "status": "error",
                    "error": error_message,
                    "reasoning": reasoning_history,
                    "tool_outputs": tool_outputs,
                    "final_response": "",
                }

            iteration = 0
            max_iterations = 12

            while iteration < max_iterations:
                reasoning = getattr(orchestrator_output, "reasoning", None)
                if reasoning:
                    reasoning_history.append(reasoning)
                    await _maybe_emit_stream(f"Step {iteration + 1} reasoning:\n{reasoning}\n\n", stream_callback)

                action_instance = getattr(orchestrator_output, "action", None)
                if action_instance is None:
                    error_message = "Orchestrator returned no action"
                    await _maybe_emit_stream(f"{error_message}\n", stream_callback)
                    break

                if isinstance(action_instance, FinalResponseSchema):
                    final_response = action_instance.response_text
                    await _maybe_emit_stream(f"Final analysis:\n{final_response}\n", stream_callback)
                    await _maybe_emit_tool_event(
                        {
                            "type": "tool_call_result",
                            "tool_name": "mysql_orchestrator",
                            "result": {"content": [final_response], "is_error": False},
                            "timestamp": _now(),
                        }
                    )
                    break

                schema_type = type(action_instance)
                ToolClass = tool_schema_to_class_map.get(schema_type)

                if not ToolClass:
                    error_message = f"Unsupported action schema: {schema_type.__name__}"
                    await _maybe_emit_stream(f"{error_message}\n", stream_callback)
                    await _maybe_emit_tool_event(
                        {
                            "type": "tool_call_error",
                            "tool_name": schema_type.__name__,
                            "error": error_message,
                            "timestamp": _now(),
                        }
                    )
                    break

                tool_name = getattr(ToolClass, "mcp_tool_name", schema_type.__name__)
                tool_args = {}
                try:
                    if hasattr(action_instance, "model_dump"):
                        tool_args = action_instance.model_dump()
                except Exception:  # pragma: no cover - defensive
                    tool_args = {}

                await _maybe_emit_tool_event(
                    {
                        "type": "tool_call_start",
                        "tool_name": tool_name,
                        "args": tool_args,
                        "timestamp": _now(),
                    }
                )
                if tool_args:
                    try:
                        args_text = json.dumps(tool_args, ensure_ascii=False)
                    except TypeError:
                        args_text = str(tool_args)
                    await _maybe_emit_stream(
                        f"Executing {tool_name} with args: {args_text}\n",
                        stream_callback,
                    )
                else:
                    await _maybe_emit_stream(f"Executing {tool_name}...\n", stream_callback)

                tool_instance = ToolClass()
                try:
                    tool_output_obj = await asyncio.to_thread(tool_instance.run, action_instance)
                except Exception as tool_exc:
                    error_message = str(tool_exc)
                    await _maybe_emit_stream(f"{tool_name} failed: {error_message}\n", stream_callback)
                    await _maybe_emit_tool_event(
                        {
                            "type": "tool_call_error",
                            "tool_name": tool_name,
                            "error": error_message,
                            "timestamp": _now(),
                        }
                    )
                    break

                raw_result = getattr(tool_output_obj, "result", tool_output_obj)
                normalized_result = _normalize_tool_result(raw_result)

                tool_outputs.append(
                    {
                        "tool_name": tool_name,
                        "normalized": normalized_result,
                    }
                )

                if isinstance(normalized_result, list):
                    event_content = normalized_result
                elif isinstance(normalized_result, dict):
                    event_content = [normalized_result]
                else:
                    event_content = [normalized_result]

                await _maybe_emit_tool_event(
                    {
                        "type": "tool_call_result",
                        "tool_name": tool_name,
                        "result": {
                            "content": event_content,
                            "is_error": False,
                        },
                        "timestamp": _now(),
                    }
                )

                await _maybe_emit_stream(
                    f"{tool_name} completed.\n{normalized_result}\n\n",
                    stream_callback,
                )

                # Add tool result context for next iteration
                try:
                    result_message = MCPOrchestratorInputSchema(
                        query=f"Tool {tool_name} executed with result: {normalized_result}"
                    )
                    orchestrator_agent.memory.add_message("system", result_message)
                except Exception as memory_error:  # pragma: no cover - logging only
                    logger.debug(f"Failed to record tool result: {memory_error}")

                orchestrator_output = await _run_orchestrator_next()
                iteration += 1

            if final_response is None and error_message is None:
                error_message = f"Analysis incomplete after {max_iterations} iterations"
                await _maybe_emit_tool_event(
                    {
                        "type": "tool_call_error",
                        "tool_name": "mysql_orchestrator",
                        "error": error_message,
                        "timestamp": _now(),
                    }
                )
                await _maybe_emit_stream(f"{error_message}\n", stream_callback)

            success = final_response is not None and error_message is None

            result_payload: Dict[str, Any] = {
                "success": success,
                "status": "success" if success else "error",
                "reasoning": reasoning_history,
                "tool_outputs": tool_outputs,
                "final_response": final_response or "",
            }

            if error_message:
                result_payload["error"] = error_message

            if tool_outputs:
                result_payload["data"] = {
                    "tool_outputs": [
                        {
                            "tool_name": output.get("tool_name"),
                            "result": output.get("normalized"),
                        }
                        for output in tool_outputs
                    ]
                }

            return result_payload

        async def process_message_streaming(
            message: str,
            stream_callback: Callable[[Any], Any],
        ) -> str:
            """Streaming-friendly wrapper used by WebSocket sessions."""
            result = await process_query(message, stream_callback=stream_callback)
            final_text = result.get("final_response") or result.get("error") or ""
            return final_text

        # Attach streaming helpers to orchestrator agent
        orchestrator_agent.process_query = process_query  # type: ignore[attr-defined]
        orchestrator_agent.process_message_streaming = process_message_streaming  # type: ignore[attr-defined]

        return orchestrator_agent

    except Exception as e:
        logger.error(f"Failed to create MySQL orchestrator agent: {e}")
        raise


# Interactive chat function (matches reference pattern)
def run_mysql_chat():
    """
    Run interactive chat with MySQL MCP Orchestrator Agent.
    Matches the reference pattern from client.py.
    """
    if not ATOMIC_COMPONENTS_AVAILABLE:
        print("Error: atomic-agents components not available. Please install atomic-agents.")
        return

    # Initialize tools lazily
    _initialize_tools()

    try:
        # Create the orchestrator agent
        orchestrator_agent = create_mysql_orchestrator_agent()

        print("MySQL MCP Agent Interactive Chat (SSE mode). Type 'exit' or 'quit' to leave.")
        while True:
            query = input("You: ").strip()
            if query.lower() in {"exit", "quit"}:
                print("Exiting chat. Goodbye!")
                break
            if not query:
                continue  # Ignore empty input

            try:
                # Initial run with user query
                orchestrator_output = safe_orchestrator_run(orchestrator_agent, MCPOrchestratorInputSchema(query=query))

                action_instance = orchestrator_output.action
                reasoning = orchestrator_output.reasoning
                print(f"Orchestrator reasoning: {reasoning}")

                # Keep executing until we get a final response
                while not isinstance(action_instance, FinalResponseSchema):
                    schema_type = type(action_instance)
                    ToolClass = tool_schema_to_class_map.get(schema_type)
                    if not ToolClass:
                        raise ValueError(f"Unknown schema type '{schema_type.__name__}' returned by orchestrator")

                    tool_name = ToolClass.mcp_tool_name
                    print(f"Executing tool: {tool_name}")
                    print(f"Parameters: {action_instance.model_dump()}")

                    tool_instance = ToolClass()
                    tool_output = tool_instance.run(action_instance)
                    print(f"Result: {tool_output.result}")

                    # Add tool result to agent memory
                    result_message = MCPOrchestratorInputSchema(
                        query=(f"Tool {tool_name} executed with result: {tool_output.result}")
                    )
                    orchestrator_agent.memory.add_message("system", result_message)

                    # Run the agent again without parameters to continue the flow
                    orchestrator_output = safe_orchestrator_run(orchestrator_agent)
                    action_instance = orchestrator_output.action
                    reasoning = orchestrator_output.reasoning
                    print(f"Orchestrator reasoning: {reasoning}")

                # Final response from the agent
                print(f"Agent: {action_instance.response_text}")

            except Exception as e:
                print(f"Error processing query: {str(e)}")

    except Exception as e:
        print(f"Fatal error: {str(e)}")


# Main entry point
if __name__ == "__main__":
    run_mysql_chat()
