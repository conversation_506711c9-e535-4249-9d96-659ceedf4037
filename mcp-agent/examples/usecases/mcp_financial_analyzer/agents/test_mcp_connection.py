#!/usr/bin/env python3
"""
Test MCP server connection directly to see what tools are available.
"""

import asyncio
import sys
import traceback
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_mcp_connection():
    """Test direct connection to the MCP server."""
    print("=== Testing MCP Server Connection ===")
    
    try:
        # Import MCP client components
        from mcp.client.sse import sse_client
        from mcp import ClientSession
        
        print("✓ Successfully imported MCP client components")
        
        # Connect to the MySQL MCP server
        mcp_server_url = "http://localhost:8702"
        sse_endpoint = f"{mcp_server_url}/sse"
        
        print(f"Connecting to MCP server at: {sse_endpoint}")
        
        async with sse_client(sse_endpoint) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                print("✓ Connected to MCP server")
                
                # Initialize the session
                await session.initialize()
                print("✓ Session initialized")
                
                # List available tools
                print("Listing available tools...")
                tools_result = await session.list_tools()
                
                if tools_result and tools_result.tools:
                    print(f"✓ Found {len(tools_result.tools)} tools:")
                    for i, tool in enumerate(tools_result.tools, 1):
                        print(f"  {i}. {tool.name}")
                        print(f"     Description: {tool.description}")
                        if hasattr(tool, 'inputSchema') and tool.inputSchema:
                            print(f"     Input Schema: {tool.inputSchema}")
                        print()
                else:
                    print("✗ No tools found")
                    return False
                
                # Test calling a tool if available
                if tools_result and tools_result.tools:
                    first_tool = tools_result.tools[0]
                    print(f"Testing tool call: {first_tool.name}")
                    
                    try:
                        # Try calling the first tool with a simple query
                        result = await session.call_tool(
                            name=first_tool.name,
                            arguments={"input_data": {"query": "SHOW TABLES;"}}
                        )
                        print(f"✓ Tool call successful")
                        print(f"  Result: {result}")
                        
                    except Exception as e:
                        print(f"✗ Tool call failed: {e}")
                        print(f"  Error type: {type(e)}")
                        traceback.print_exc()
                
                return True
                
    except Exception as e:
        print(f"✗ MCP connection test failed: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

async def test_fetch_mcp_tools():
    """Test the fetch_mcp_tools function directly."""
    print("\n=== Testing fetch_mcp_tools Function ===")
    
    try:
        # Import the function
        from atomic_agents.lib.factories.mcp_tool_factory import fetch_mcp_tools
        
        print("✓ Successfully imported fetch_mcp_tools")
        
        # Test the function
        mcp_server_url = "http://localhost:8702"
        print(f"Calling fetch_mcp_tools with endpoint: {mcp_server_url}")
        
        tools = fetch_mcp_tools(
            mcp_endpoint=mcp_server_url,
            use_stdio=False,
        )
        
        print(f"✓ fetch_mcp_tools returned: {tools}")
        print(f"  Type: {type(tools)}")
        print(f"  Length: {len(tools) if tools else 'None'}")
        
        if tools:
            for i, tool in enumerate(tools, 1):
                print(f"  {i}. {tool}")
                print(f"     Type: {type(tool)}")
                if hasattr(tool, 'input_schema'):
                    print(f"     Input Schema: {tool.input_schema}")
                if hasattr(tool, 'mcp_tool_name'):
                    print(f"     MCP Tool Name: {tool.mcp_tool_name}")
                print()
        
        return len(tools) > 0 if tools else False
        
    except Exception as e:
        print(f"✗ fetch_mcp_tools test failed: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("Starting MCP connection tests...\n")
    
    # Test 1: Direct MCP connection
    connection_success = await test_mcp_connection()
    
    # Test 2: fetch_mcp_tools function
    fetch_success = await test_fetch_mcp_tools()
    
    # Summary
    print("\n=== Test Summary ===")
    print(f"Direct MCP connection test: {'✓ PASS' if connection_success else '✗ FAIL'}")
    print(f"fetch_mcp_tools test: {'✓ PASS' if fetch_success else '✗ FAIL'}")
    
    if connection_success and fetch_success:
        print("✓ All tests passed - MCP server is accessible and tools are available")
        return 0
    elif connection_success and not fetch_success:
        print("⚠ MCP server is accessible but fetch_mcp_tools is not working")
        return 1
    else:
        print("✗ MCP server connection failed")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
