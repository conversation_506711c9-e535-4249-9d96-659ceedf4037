#!/usr/bin/env python3
"""
Test script to verify the MySQL agent schema validation fix.
This script tests the ActionUnion creation without starting the full server.
"""

import sys
import os
import asyncio
from typing import Union as TypingUnion

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from atomic_agents.lib.base.base_io_schema import BaseIOSchema
from pydantic import Field

# Mock the FinalResponseSchema
class FinalResponseSchema(BaseIOSchema):
    """Final response schema for testing."""
    response: str = Field(..., description="Final response")

# Mock the MySQLAgentInputSchema
class MySQLAgentInputSchema(BaseIOSchema):
    """MySQL agent input schema for testing."""
    query: str = Field(..., description="SQL query to execute")

def test_action_union_creation():
    """Test the ActionUnion creation logic that was fixed."""
    print("Testing ActionUnion creation fix...")
    
    # Test case 1: Single schema
    print("\n1. Testing single schema case:")
    available_schemas = (FinalResponseSchema,)
    if len(available_schemas) == 1:
        ActionUnion = available_schemas[0]
    else:
        ActionUnion = TypingUnion[available_schemas]
    
    print(f"   Available schemas: {[s.__name__ for s in available_schemas]}")
    print(f"   ActionUnion: {ActionUnion}")
    print(f"   ActionUnion type: {type(ActionUnion)}")
    
    # Test case 2: Multiple schemas (the fixed case)
    print("\n2. Testing multiple schemas case (FIXED):")
    available_schemas = (MySQLAgentInputSchema, FinalResponseSchema)
    if len(available_schemas) == 1:
        ActionUnion = available_schemas[0]
    else:
        # This is the FIXED version - using unpacking
        ActionUnion = TypingUnion[available_schemas]
    
    print(f"   Available schemas: {[s.__name__ for s in available_schemas]}")
    print(f"   ActionUnion: {ActionUnion}")
    print(f"   ActionUnion type: {type(ActionUnion)}")
    
    # Test case 3: The BROKEN version (for comparison)
    print("\n3. Testing multiple schemas case (BROKEN - for comparison):")
    available_schemas = (MySQLAgentInputSchema, FinalResponseSchema)
    try:
        if len(available_schemas) == 1:
            ActionUnion = available_schemas[0]
        else:
            # This is the BROKEN version - using tuple()
            ActionUnion = TypingUnion[tuple(available_schemas)]
        
        print(f"   Available schemas: {[s.__name__ for s in available_schemas]}")
        print(f"   ActionUnion: {ActionUnion}")
        print(f"   ActionUnion type: {type(ActionUnion)}")
        print("   ❌ BROKEN version didn't fail (unexpected)")
    except Exception as e:
        print(f"   ❌ BROKEN version failed as expected: {e}")
    
    print("\n✅ ActionUnion creation test completed successfully!")

def test_schema_validation():
    """Test that the fixed ActionUnion can be used for validation."""
    print("\nTesting schema validation with fixed ActionUnion...")
    
    # Create the fixed ActionUnion
    available_schemas = (MySQLAgentInputSchema, FinalResponseSchema)
    ActionUnion = TypingUnion[available_schemas]
    
    # Create a test schema that uses the ActionUnion
    class TestOutputSchema(BaseIOSchema):
        """Test output schema."""
        reasoning: str = Field(..., description="Reasoning")
        action: ActionUnion = Field(..., description="Action to take")
    
    # Test valid instances
    print("\n1. Testing valid MySQL action:")
    try:
        mysql_action = MySQLAgentInputSchema(query="SELECT * FROM inventory WHERE part_id = 'GPU MM2004IC001'")
        test_output = TestOutputSchema(
            reasoning="Need to check inventory for GPU MM2004IC001",
            action=mysql_action
        )
        print(f"   ✅ MySQL action validated successfully: {test_output.action.query}")
    except Exception as e:
        print(f"   ❌ MySQL action validation failed: {e}")
    
    print("\n2. Testing valid final response:")
    try:
        final_response = FinalResponseSchema(response="Analysis complete")
        test_output = TestOutputSchema(
            reasoning="Analysis is finished",
            action=final_response
        )
        print(f"   ✅ Final response validated successfully: {test_output.action.response}")
    except Exception as e:
        print(f"   ❌ Final response validation failed: {e}")
    
    print("\n✅ Schema validation test completed successfully!")

def main():
    """Main test function."""
    print("=" * 60)
    print("MySQL Agent Schema Validation Fix Test")
    print("=" * 60)
    
    try:
        test_action_union_creation()
        test_schema_validation()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! The fix is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
