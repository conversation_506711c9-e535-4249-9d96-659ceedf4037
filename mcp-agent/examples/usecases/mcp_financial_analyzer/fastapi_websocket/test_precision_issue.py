#!/usr/bin/env python3
"""
Test script to reproduce the exact floating-point precision issue with WeightedShortageIndex.
"""

import asyncio
import math
import sys
import os

# Add the parent directory to the path to import the agents
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.shortage_analyzer_agent import create_shortage_analyzer_agent, MCPToolIntegration


def test_precision_scenarios():
    """Test different floating-point precision scenarios."""
    print("=== Testing Floating-Point Precision Scenarios ===")
    
    # Scenario 1: Weights that are close to 1.0 but not exactly 1.0
    weights1 = [0.33333333333333333, 0.33333333333333333, 0.33333333333333334]
    total1 = sum(weights1)
    print(f"\nScenario 1:")
    print(f"  Weights: {weights1}")
    print(f"  Sum: {total1}")
    print(f"  Sum == 1.0: {total1 == 1.0}")
    print(f"  math.isclose(sum, 1.0, rel_tol=1e-6): {math.isclose(total1, 1.0, rel_tol=1e-6)}")
    
    # Scenario 2: Weights that sum to 0.9999999999999999
    weights2 = [0.1, 0.2, 0.3, 0.39999999999999997]
    total2 = sum(weights2)
    print(f"\nScenario 2:")
    print(f"  Weights: {weights2}")
    print(f"  Sum: {total2}")
    print(f"  Sum == 1.0: {total2 == 1.0}")
    print(f"  math.isclose(sum, 1.0, rel_tol=1e-6): {math.isclose(total2, 1.0, rel_tol=1e-6)}")
    
    # Scenario 3: Weights that sum to 1.0000000000000002
    weights3 = [0.1, 0.2, 0.3, 0.4000000000000001]
    total3 = sum(weights3)
    print(f"\nScenario 3:")
    print(f"  Weights: {weights3}")
    print(f"  Sum: {total3}")
    print(f"  Sum == 1.0: {total3 == 1.0}")
    print(f"  math.isclose(sum, 1.0, rel_tol=1e-6): {math.isclose(total3, 1.0, rel_tol=1e-6)}")
    
    # Scenario 4: Create weights that will definitely trigger the issue
    # These weights sum to something very close to 1.0 but not exactly 1.0
    weights4 = [0.2, 0.3, 0.5 - 1e-15]  # This should sum to 0.9999999999999999
    total4 = sum(weights4)
    print(f"\nScenario 4:")
    print(f"  Weights: {weights4}")
    print(f"  Sum: {total4}")
    print(f"  Sum == 1.0: {total4 == 1.0}")
    print(f"  math.isclose(sum, 1.0, rel_tol=1e-6): {math.isclose(total4, 1.0, rel_tol=1e-6)}")
    
    return [weights1, weights2, weights3, weights4]


async def test_weighted_shortage_precision():
    """Test the WeightedShortageIndex tool with precision issues."""
    print("\n=== Testing WeightedShortageIndex with Precision Issues ===")
    
    # Initialize the shortage analyzer agent
    agent = create_shortage_analyzer_agent("test_company")
    mcp_integration = MCPToolIntegration(agent)
    
    # Get test scenarios
    test_weights = test_precision_scenarios()
    
    for i, weights in enumerate(test_weights, 1):
        print(f"\n--- Testing Scenario {i} ---")
        print(f"Weights: {weights}")
        print(f"Sum: {sum(weights)}")
        
        try:
            result = await mcp_integration.call_weighted_shortage_tool(
                required_qty=[1000, 2000, 3000],
                available_qty=[500, 1000, 1500],
                weights=weights
            )
            print(f"✅ SUCCESS: {result}")
        except Exception as e:
            print(f"❌ ERROR: {e}")


async def test_direct_tool_validation():
    """Test the tool validation logic directly."""
    print("\n=== Testing Direct Tool Validation Logic ===")
    
    # Test the exact validation logic from the WeightedShortageIndex tool
    test_weights = [
        [0.33333333333333333, 0.33333333333333333, 0.33333333333333334],
        [0.1, 0.2, 0.3, 0.39999999999999997],
        [0.1, 0.2, 0.3, 0.4000000000000001],
        [0.2, 0.3, 0.5 - 1e-15]
    ]
    
    for i, weights in enumerate(test_weights, 1):
        total = sum(weights)
        print(f"\nScenario {i}:")
        print(f"  Weights: {weights}")
        print(f"  Sum: {total}")
        print(f"  Exact equality check (sum != 1.0): {total != 1.0}")
        print(f"  Would fail validation: {'YES' if total != 1.0 else 'NO'}")
        
        # Test the normalization logic
        if math.isclose(total, 1.0, rel_tol=1e-6):
            print(f"  Normalization: SKIPPED (considered close enough)")
            print(f"  Final weights: {weights}")
        else:
            normalized = [w / total for w in weights]
            print(f"  Normalization: APPLIED")
            print(f"  Final weights: {normalized}")
            print(f"  Final sum: {sum(normalized)}")


async def main():
    """Main test function."""
    await test_weighted_shortage_precision()
    await test_direct_tool_validation()


if __name__ == "__main__":
    asyncio.run(main())
