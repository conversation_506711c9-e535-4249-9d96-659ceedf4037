#!/usr/bin/env python3
"""
Test script to reproduce the WeightedShortageIndex "Weights must sum to 1" error.
"""

import asyncio
import json
import sys
import os
import websockets
import argparse
from datetime import datetime

# Add the parent directory to the path to import the agents
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.shortage_analyzer_agent import create_shortage_analyzer_agent, MCPToolIntegration


async def test_weighted_shortage_direct():
    """Test the WeightedShortageIndex tool directly to reproduce the error."""
    print("=== Testing WeightedShortageIndex Tool Directly ===")

    # Initialize the shortage analyzer agent
    agent = create_shortage_analyzer_agent("test_company")

    # Create MCP tool integration for direct testing
    mcp_integration = MCPToolIntegration(agent)
    
    # Test case 1: Weights that don't sum to exactly 1.0 due to floating-point precision
    print("\n1. Testing with weights that have floating-point precision issues:")
    weights_precision_issue = [0.1, 0.2, 0.3, 0.4]  # These might not sum to exactly 1.0
    print(f"   Weights: {weights_precision_issue}")
    print(f"   Sum: {sum(weights_precision_issue)}")
    print(f"   Sum == 1.0: {sum(weights_precision_issue) == 1.0}")
    
    try:
        result = await mcp_integration.call_weighted_shortage_tool(
            required_qty=[1000, 2000, 3000, 4000],
            available_qty=[500, 1000, 1500, 2000],
            weights=weights_precision_issue
        )
        print(f"   Result: {result}")
    except Exception as e:
        print(f"   Error: {e}")

    # Test case 2: Weights that clearly don't sum to 1.0
    print("\n2. Testing with weights that clearly don't sum to 1.0:")
    weights_wrong_sum = [0.3, 0.3, 0.3]  # Sum = 0.9
    print(f"   Weights: {weights_wrong_sum}")
    print(f"   Sum: {sum(weights_wrong_sum)}")

    try:
        result = await mcp_integration.call_weighted_shortage_tool(
            required_qty=[1000, 2000, 3000],
            available_qty=[500, 1000, 1500],
            weights=weights_wrong_sum
        )
        print(f"   Result: {result}")
    except Exception as e:
        print(f"   Error: {e}")

    # Test case 3: Weights with more complex floating-point precision issues
    print("\n3. Testing with complex floating-point precision issues:")
    weights_complex = [1/3, 1/3, 1/3]  # These definitely won't sum to exactly 1.0
    print(f"   Weights: {weights_complex}")
    print(f"   Sum: {sum(weights_complex)}")
    print(f"   Sum == 1.0: {sum(weights_complex) == 1.0}")

    try:
        result = await mcp_integration.call_weighted_shortage_tool(
            required_qty=[1000, 2000, 3000],
            available_qty=[500, 1000, 1500],
            weights=weights_complex
        )
        print(f"   Result: {result}")
    except Exception as e:
        print(f"   Error: {e}")


async def test_weighted_shortage_via_websocket():
    """Test the WeightedShortageIndex tool via WebSocket to reproduce the error."""
    print("\n=== Testing WeightedShortageIndex Tool via WebSocket ===")
    
    # Create a test message that should trigger WeightedShortageIndex
    test_message = {
        "message": "Calculate weighted shortage index for GPU MM2004IC001 (4000 units needed, 150 available, weight 0.4), CPU DEP2004IC001 (2000 units needed, 200 available, weight 0.35), and memory ATR6G00801 (8000 units needed, 150 available, weight 0.25). Use weighted shortage analysis.",
        "user_id": "test_user"
    }
    
    try:
        # Connect to the WebSocket server
        uri = "ws://localhost:8000/ws/shortage_analysis"
        async with websockets.connect(uri) as websocket:
            print(f"Connected to {uri}")
            
            # Send the test message
            await websocket.send(json.dumps(test_message))
            print(f"Sent message: {test_message['message']}")
            
            # Collect all responses
            responses = []
            try:
                while True:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    responses.append(response_data)
                    
                    print(f"Received: {response_data}")
                    
                    # Check if this is a WeightedShortageIndex error
                    if ("WeightedShortageIndex" in str(response_data) and 
                        "Weights must sum to 1" in str(response_data)):
                        print("*** FOUND THE ERROR! ***")
                        print(json.dumps(response_data, indent=2))
                        break
                    
                    # Check if workflow is complete
                    if (response_data.get("type") == "workflow_complete" or
                        "workflow_complete" in str(response_data)):
                        print("Workflow completed")
                        break
                        
            except asyncio.TimeoutError:
                print("Timeout waiting for more responses")
                
    except Exception as e:
        print(f"WebSocket test failed: {e}")
        print("Make sure the FastAPI server is running on localhost:8000")


async def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test WeightedShortageIndex error reproduction")
    parser.add_argument("--direct", action="store_true", help="Test direct tool calls")
    parser.add_argument("--websocket", action="store_true", help="Test via WebSocket")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    
    args = parser.parse_args()
    
    if args.all or args.direct:
        await test_weighted_shortage_direct()
    
    if args.all or args.websocket:
        await test_weighted_shortage_via_websocket()
    
    if not any([args.direct, args.websocket, args.all]):
        print("No test specified. Use --direct, --websocket, or --all")
        parser.print_help()


if __name__ == "__main__":
    asyncio.run(main())
