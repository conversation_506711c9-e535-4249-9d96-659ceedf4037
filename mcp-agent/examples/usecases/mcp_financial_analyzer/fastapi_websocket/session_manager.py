import asyncio
import inspect
import os
import uuid
import json
import sys
import time
import logging
from typing import Dict, Optional, AsyncGenerator, Callable, Any, List, Tuple
from datetime import datetime
from enum import Enum
from pathlib import Path

from mcp_agent.app import MCPApp
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
from mcp_agent.workflows.llm.augmented_llm import CallToolRequest, AugmentedLLM
from openai.types.chat import ChatCompletionMessageToolCall
from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
from mcp_agent.workflows.llm.augmented_llm import RequestParams
from mcp_agent.workflows.evaluator_optimizer.evaluator_optimizer import (
    EvaluatorOptimizerLLM,
    QualityRating,
)

# Import agent factories from parent directory
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from agents.research_agent import create_research_agent
from agents.analyst_agent import create_analyst_agent
from agents.report_writer import create_report_writer

# Configure logger
logger = logging.getLogger(__name__)

# Import orchestration components
from orchestrator.orchestration_runner import OrchestrationRunner, create_orchestration_runner
from orchestrator.context_manager import ContextManager
from orchestrator.query_processor import FinancialQueryProcessor

# Import orchestration agent factories
from agents.mysql_agent import create_mysql_orchestrator_agent
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent

# Import agent orchestration manager
from agents.agent_interfaces import AgentOrchestrationManager

# Import server management (avoiding circular import by importing at runtime)
# from main import DualServerManager

# Create specialized LLM debug logger
llm_debug_logger = logging.getLogger('llm_debug')


def setup_comprehensive_logging(show_llm_realtime=True):
    """Set up comprehensive logging with LLM debug support and log file management.

    Args:
        show_llm_realtime: If True, display LLM reasoning and output in real-time on console
    """
    # Ensure logs directory exists
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # Create timestamped log file for this session
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    debug_log_file = logs_dir / f"websocket_orchestration_debug_{timestamp}.log"

    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(name)s - [%(filename)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(debug_log_file, mode='w')
        ]
    )

    # Create specialized LLM debug logger with enhanced formatting
    llm_debug_logger.setLevel(logging.DEBUG)

    # Create dedicated LLM debug file handler
    llm_debug_file = logs_dir / f"websocket_llm_reasoning_debug_{timestamp}.log"
    llm_file_handler = logging.FileHandler(llm_debug_file, mode='w')
    llm_file_handler.setLevel(logging.DEBUG)
    llm_file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - LLM_DEBUG - %(message)s'
    ))
    llm_debug_logger.addHandler(llm_file_handler)

    # Add console handler for real-time LLM debug display if requested
    if show_llm_realtime:
        llm_console_handler = logging.StreamHandler(sys.stdout)
        llm_console_handler.setLevel(logging.DEBUG)
        # Use a more concise format for console display
        llm_console_handler.setFormatter(logging.Formatter(
            '\n%(message)s'
        ))
        llm_debug_logger.addHandler(llm_console_handler)

    return debug_log_file, llm_debug_file


def log_llm_interaction(interaction_type: str, agent_name: str = "unknown",
                       query: str = "", response: str = "", reasoning: str = "",
                       execution_time: float = 0.0, extra_data: Dict[str, Any] = None,
                       thinking_content: str = "", raw_completion: str = ""):
    """Enhanced LLM debug logging with comprehensive reasoning and output capture."""
    separator = "=" * 80
    timestamp = datetime.now().isoformat()

    # Create detailed log entry for file
    detailed_log = []
    detailed_log.append(f"\n{separator}")
    detailed_log.append(f"LLM INTERACTION: {interaction_type.upper()}")
    detailed_log.append(f"Agent: {agent_name}")
    detailed_log.append(f"Timestamp: {timestamp}")
    detailed_log.append(f"Execution Time: {execution_time:.3f}s")

    if query:
        detailed_log.append(f"\nINPUT QUERY:")
        detailed_log.append(f"{query}")

    if thinking_content:
        detailed_log.append(f"\nLLM THINKING PROCESS (Qwen Reasoning):")
        detailed_log.append(f"{thinking_content}")

    if reasoning:
        detailed_log.append(f"\nLLM REASONING PROCESS:")
        detailed_log.append(f"{reasoning}")

    if raw_completion:
        detailed_log.append(f"\nRAW LLM COMPLETION:")
        detailed_log.append(f"{raw_completion}")

    if response:
        detailed_log.append(f"\nLLM OUTPUT RESPONSE:")
        detailed_log.append(f"{response}")

    if extra_data:
        detailed_log.append(f"\nADDITIONAL DATA:")
        for key, value in extra_data.items():
            if isinstance(value, (dict, list)):
                try:
                    formatted_value = json.dumps(value, indent=2, default=str)
                    detailed_log.append(f"  {key}:\n{formatted_value}")
                except:
                    detailed_log.append(f"  {key}: {value}")
            else:
                detailed_log.append(f"  {key}: {value}")

    detailed_log.append(f"{separator}\n")

    # Log the detailed version (goes to both file and console if real-time is enabled)
    for line in detailed_log:
        llm_debug_logger.debug(line)


class SessionType(Enum):
    """Types of financial analysis sessions."""
    BASIC = "basic"
    RESEARCH = "research"
    ANALYZE = "analyze"
    REPORT = "report"
    FULL_ANALYSIS = "full_analysis"
    ORCHESTRATION = "orchestration"
    DEMO = "demo"
    MYSQL = "mysql"
    STORAGE_ANALYZER = "storage_analyzer"
    ALERT_MANAGER = "alert_manager"


class StreamingOpenAILLM:
    pass


class VLLMResponseInterceptor:
    """Middleware layer that intercepts VLLM responses before tool parsing for early streaming."""

    def __init__(self, early_stream_callback: Callable[[Dict[str, Any]], None] = None):
        """
        Initialize the VLLM response interceptor.

        Args:
            early_stream_callback: Callback for streaming VLLM responses before tool parsing
        """
        self.early_stream_callback = early_stream_callback
        self.buffered_response = ""
        self.chunk_index = 0
        self.total_chars = 0
        self.reasoning_phase_active = False

    async def intercept_and_stream_response(
        self,
        llm: VLLMAugmentedLLM,
        message: str,
        request_params: RequestParams | None = None,
        **kwargs
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Intercept VLLM response, stream it early, then parse tools.

        Returns:
            Tuple of (full_response, parsed_tool_calls)
        """
        self.buffered_response = ""
        self.chunk_index = 0
        self.total_chars = 0
        self.reasoning_phase_active = True

        # Signal start of VLLM reasoning phase
        await self._emit_early_stream({
            "type": "vllm_response_start",
            "message": "Starting VLLM reasoning analysis...",
            "phase": "reasoning",
            "timestamp": self._timestamp()
        })

        try:
            # Custom stream handler to capture and forward chunks
            async def early_chunk_handler(chunk: str | None) -> None:
                if chunk is None:
                    return

                self.buffered_response += chunk
                self.chunk_index += 1
                chunk_length = len(chunk)
                self.total_chars += chunk_length

                # Stream the chunk immediately
                await self._emit_early_stream({
                    "type": "vllm_response_chunk",
                    "message": {
                        "content": chunk,
                        "chunk_index": self.chunk_index,
                        "chunk_chars": chunk_length,
                        "cumulative_chars": self.total_chars
                    },
                    "phase": "reasoning",
                    "source": "vllm_pre_tool",
                    "timestamp": self._timestamp()
                })

            # Generate with early streaming when supported by the LLM API
            def _accepts_param(func, name: str) -> bool:
                try:
                    sig = inspect.signature(func)
                except (TypeError, ValueError):
                    return False
                params = sig.parameters
                if name in params:
                    return True
                # Accept if function has **kwargs
                return any(p.kind == inspect.Parameter.VAR_KEYWORD for p in params.values())

            gen_kwargs: Dict[str, Any] = {"message": message, "request_params": request_params}
            if _accepts_param(llm.generate, "enable_thinking"):
                gen_kwargs["enable_thinking"] = True
            if _accepts_param(llm.generate, "stream_callback"):
                gen_kwargs["stream_callback"] = early_chunk_handler
            # Propagate any extra kwargs that may be supported
            gen_kwargs.update(kwargs or {})

            responses = await llm.generate(**gen_kwargs)

            # Collect full response
            full_response = ""
            for response in responses:
                if response.content:
                    full_response += str(response.content)

            # Use buffered response if generation didn't return content
            if not full_response and self.buffered_response:
                full_response = self.buffered_response

            # Parse tool calls from complete response
            tool_calls = await self._parse_tool_calls(full_response, llm)

            # Signal reasoning phase complete
            await self._emit_early_stream({
                "type": "vllm_response_complete",
                "message": {
                    "full_response": full_response,
                    "tool_count": len(tool_calls),
                    "total_chunks": self.chunk_index,
                    "total_chars": self.total_chars
                },
                "phase": "reasoning_complete",
                "timestamp": self._timestamp()
            })

            # Signal transition to tool execution if tools found
            if tool_calls:
                await self._emit_early_stream({
                    "type": "tool_execution_start",
                    "message": f"Beginning execution of {len(tool_calls)} MCP tools...",
                    "phase": "tool_execution",
                    "tool_count": len(tool_calls),
                    "timestamp": self._timestamp()
                })

            self.reasoning_phase_active = False
            return full_response, tool_calls

        except asyncio.TimeoutError as e:
            self.reasoning_phase_active = False
            await self._emit_early_stream({
                "type": "vllm_response_error",
                "message": f"VLLM reasoning timeout: {str(e)}",
                "phase": "reasoning_timeout",
                "timestamp": self._timestamp(),
                "fallback_available": True
            })
            # Re-raise to trigger fallback to traditional streaming
            raise
        except Exception as e:
            self.reasoning_phase_active = False
            await self._emit_early_stream({
                "type": "vllm_response_error",
                "message": f"Error during VLLM reasoning: {str(e)}",
                "phase": "reasoning_error",
                "timestamp": self._timestamp(),
                "fallback_available": True
            })
            # Re-raise to trigger fallback to traditional streaming
            raise

    async def _parse_tool_calls(self, response: str, llm: VLLMAugmentedLLM) -> List[Dict[str, Any]]:
        """Parse tool calls from the complete VLLM response."""
        tool_calls = []

        try:
            # Use the LLM's existing tool parsing logic if available
            if hasattr(llm, '_parse_tool_calls_from_response'):
                tool_calls = await llm._parse_tool_calls_from_response(response)
            elif hasattr(llm, 'extract_tool_calls'):
                tool_calls = llm.extract_tool_calls(response)
            else:
                # Basic tool call detection (looking for function call patterns)
                import re
                tool_pattern = r'<tool_call>.*?</tool_call>|function_call\s*\([^)]+\)'
                matches = re.findall(tool_pattern, response, re.DOTALL)
                tool_calls = [{"raw": match, "parsed": False} for match in matches]

        except Exception as e:
            logger.warning(f"Tool parsing failed: {e}")

        return tool_calls

    async def _emit_early_stream(self, payload: Dict[str, Any]) -> None:
        """Emit early streaming data if callback is available."""
        if self.early_stream_callback:
            try:
                if asyncio.iscoroutinefunction(self.early_stream_callback):
                    await self.early_stream_callback(payload)
                else:
                    self.early_stream_callback(payload)
            except Exception as e:
                logger.warning(f"Early stream callback failed: {e}")

    def _timestamp(self) -> str:
        """Generate ISO timestamp."""
        return datetime.utcnow().isoformat() + "Z"


class StreamingVLLMLLM:
    """Wrapper for VLLMAugmentedLLM that provides real streaming capabilities with early VLLM response interception."""

    def __init__(self, llm: VLLMAugmentedLLM, api_base: str = None, api_key: str = None, mcp_streaming_wrapper=None, early_stream_callback: Callable[[Dict[str, Any]], None] = None):
        self.llm = llm
        self.mcp_streaming_wrapper = mcp_streaming_wrapper
        self.early_stream_callback = early_stream_callback

        # Initialize the VLLM response interceptor for early streaming
        self.response_interceptor = VLLMResponseInterceptor(early_stream_callback)

        # Handle both real LLM objects and mocks
        if hasattr(llm, 'vllm_api_base'):
            default_api_base = getattr(llm, 'vllm_api_base', 'http://192.168.1.54:38701/v1')
        else:
            default_api_base = 'http://192.168.1.54:38701/v1'

        if hasattr(llm, 'vllm_api_key'):
            default_api_key = getattr(llm, 'vllm_api_key', 'EMPTY')
        else:
            default_api_key = 'EMPTY'

        # Allow environment overrides for VLLM connectivity
        env_api_base = os.getenv('VLLM_API_BASE')
        env_api_key = os.getenv('VLLM_API_KEY')

        # Ensure we have string values, not Mock objects
        self.api_base = (
            api_base
            or env_api_base
            or (default_api_base if isinstance(default_api_base, str) else 'http://192.168.1.54:38701/v1')
        )
        self.api_key = (
            api_key
            or env_api_key
            or (default_api_key if isinstance(default_api_key, str) else 'EMPTY')
        )

        # Create OpenAI client for streaming
        from openai import OpenAI
        try:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.api_base
            )
        except Exception as e:
            # For testing, create a mock client
            pass
            # from unittest.mock import Mock
            # self.client = Mock()
            # self.client.chat = Mock()
            # self.client.chat.completions = Mock()
            # self.client.chat.completions.create = Mock()

    async def generate_str_streaming(
        self,
        message: str,
        request_params: RequestParams | None = None,
        stream_callback: Callable[[Any], Any] | None = None,
        model: str = None,
        **kwargs
    ) -> str:
        """Generate a streaming response using VLLMAugmentedLLM with two-phase streaming: early VLLM response + tool execution."""

        def _timestamp() -> str:
            return datetime.utcnow().isoformat() + "Z"

        async def _emit_tool_stream(label: str, content: str, **extra: Any) -> None:
            """Emit tool execution streaming data (traditional streaming)."""
            if not stream_callback:
                return

            safe_content = content if isinstance(content, str) else str(content)
            payload: Dict[str, Any] = {
                "type": label.lower().replace(" ", "_"),
                "label": label,
                "content": safe_content,
                "timestamp": _timestamp(),
                "phase": "tool_execution"
            }

            if extra:
                payload.update(extra)

            result = stream_callback(payload)
            if inspect.isawaitable(result):
                await result

        async def _emit_final_llm_chunks(text: str, *, source: str = "vllm_final") -> None:
            """Emit the final LLM response as traditional stream chunks for compatibility."""
            if not stream_callback or not text:
                return

            base_index = getattr(self.response_interceptor, "chunk_index", 0)
            base_total = getattr(self.response_interceptor, "total_chars", 0)

            chunk_index = base_index
            total_chars = base_total
            chunk_size = 400

            for start in range(0, len(text), chunk_size):
                chunk = text[start : start + chunk_size]
                chunk_index += 1
                total_chars += len(chunk)

                payload = {
                    "type": "llm_response",
                    "label": "LLM response",
                    "content": chunk,
                    "timestamp": _timestamp(),
                    "chunk_index": chunk_index,
                    "metrics": {
                        "chunk_chars": len(chunk),
                        "cumulative_chars": total_chars,
                    },
                    "source": source,
                }

                result = stream_callback(payload)
                if inspect.isawaitable(result):
                    await result

            # Keep interceptor counters aligned so subsequent streaming continues numbering
            if hasattr(self.response_interceptor, "chunk_index"):
                self.response_interceptor.chunk_index = chunk_index
            if hasattr(self.response_interceptor, "total_chars"):
                self.response_interceptor.total_chars = total_chars

        # Determine fallback strategy early
        use_early_streaming = self.early_stream_callback is not None
        fallback_reason = None

        try:
            # PHASE 1: Early VLLM Response Streaming with Robust Error Handling
            if use_early_streaming:
                try:
                    # Two-phase streaming: early response + tool execution with timeout
                    full_response, tool_calls = await asyncio.wait_for(
                        self.response_interceptor.intercept_and_stream_response(
                            llm=self.llm,
                            message=message,
                            request_params=request_params,
                            **kwargs
                        ),
                        timeout=300.0  # 5 minute timeout for VLLM reasoning
                    )

                    # PHASE 2: Tool Execution Streaming with Error Recovery
                    if tool_calls and hasattr(self.llm, "execute_tool_calls"):
                        try:
                            await _emit_tool_stream("system", "Executing parsed tool calls...")

                            # Execute tool calls with traditional streaming and individual timeouts
                            for i, tool_call in enumerate(tool_calls):
                                tool_name = tool_call.get("name", f"tool_{i}")
                                await _emit_tool_stream("tool_start", f"Starting {tool_name}")

                                try:
                                    # Execute via existing tool execution mechanism with timeout
                                    result = await asyncio.wait_for(
                                        self.llm.execute_tool_call(tool_call),
                                        timeout=120.0,  # 2 minute timeout per tool
                                    )
                                    await _emit_tool_stream("tool_result", f"Tool {tool_name} completed")
                                except asyncio.TimeoutError:
                                    await _emit_tool_stream(
                                        "tool_error",
                                        f"Tool {tool_name} timed out after 2 minutes",
                                    )
                                except Exception as tool_error:
                                    await _emit_tool_stream(
                                        "tool_error", f"Tool {tool_name} failed: {str(tool_error)}"
                                    )

                        except Exception as tool_exec_error:
                            await _emit_tool_stream(
                                "error", f"Tool execution phase failed: {str(tool_exec_error)}"
                            )

                    # Always emit the final response through the standard streaming channel so
                    # legacy clients receive stream_chunk events even after early VLLM streaming.
                    await _emit_final_llm_chunks(full_response)

                    return full_response

                except asyncio.TimeoutError:
                    fallback_reason = "VLLM reasoning timeout (>5min)"
                    use_early_streaming = False
                except ConnectionError:
                    fallback_reason = "VLLM connection error"
                    use_early_streaming = False
                except Exception as early_error:
                    fallback_reason = f"VLLM early streaming error: {str(early_error)}"
                    use_early_streaming = False

            # FALLBACK: Traditional single-phase streaming with notification
            if not use_early_streaming:
                if fallback_reason and stream_callback:
                    await _emit_tool_stream("system", f"Falling back to traditional streaming: {fallback_reason}")

                return await self._traditional_streaming(
                    message=message,
                    request_params=request_params,
                    stream_callback=stream_callback,
                    **kwargs
                )

        except Exception as e:
            error_msg = f"Error processing VLLM streaming (fallback attempted): {str(e)}"
            if stream_callback:
                await _emit_tool_stream("error", error_msg)

            # Final fallback - try basic LLM generation without streaming
            try:
                if hasattr(self.llm, 'generate_str'):
                    await _emit_tool_stream("system", "Using basic LLM generation as final fallback")
                    return await self.llm.generate_str(message, request_params)
                else:
                    return f"System error: Unable to process request. {error_msg}"
            except Exception as final_error:
                return f"Critical system error: {str(final_error)}"

    async def _traditional_streaming(
        self,
        message: str,
        request_params: RequestParams | None = None,
        stream_callback: Callable[[Any], Any] | None = None,
        **kwargs
    ) -> str:
        """Fallback to traditional single-phase streaming for backward compatibility."""
        chunk_index = 0
        total_chars = 0
        streamed_chunks: List[str] = []

        def _timestamp() -> str:
            return datetime.utcnow().isoformat() + "Z"

        async def _emit(label: str, content: str, **extra: Any) -> None:
            nonlocal chunk_index, total_chars
            if not stream_callback:
                return

            safe_content = content if isinstance(content, str) else str(content)
            payload: Dict[str, Any] = {
                "type": label.lower().replace(" ", "_"),
                "label": label,
                "content": safe_content,
                "timestamp": _timestamp(),
            }

            if label.lower() == "llm response":
                chunk_index += 1
                chunk_length = len(safe_content)
                total_chars += chunk_length
                payload["chunk_index"] = chunk_index
                payload["metrics"] = {
                    "chunk_chars": chunk_length,
                    "cumulative_chars": total_chars,
                }
                payload["source"] = "vllm"

            if extra:
                payload.update(extra)

            result = stream_callback(payload)
            if inspect.isawaitable(result):
                await result

        try:
            await _emit("system", "Initializing VLLM analysis...\n\n")

            heartbeat_running = True

            async def heartbeat() -> None:
                try:
                    while heartbeat_running:
                        await asyncio.sleep(0.6)
                        await _emit("heartbeat", " ")
                except asyncio.CancelledError:
                    return

            hb_task = asyncio.create_task(heartbeat())

            async def _vllm_stream_handler(raw_chunk: str | None) -> None:
                if raw_chunk is None:
                    return
                streamed_chunks.append(raw_chunk)
                await _emit("LLM response", raw_chunk)

            used_stream_handler = False
            try:
                # Call LLM.generate with compatible parameters
                def _accepts_param(func, name: str) -> bool:
                    try:
                        sig = inspect.signature(func)
                    except (TypeError, ValueError):
                        return False
                    params = sig.parameters
                    if name in params:
                        return True
                    return any(p.kind == inspect.Parameter.VAR_KEYWORD for p in params.values())

                gen_kwargs: Dict[str, Any] = {"message": message, "request_params": request_params}
                if _accepts_param(self.llm.generate, "enable_thinking"):
                    gen_kwargs["enable_thinking"] = True
                if _accepts_param(self.llm.generate, "stream_callback"):
                    gen_kwargs["stream_callback"] = _vllm_stream_handler
                    used_stream_handler = True
                # Include any additional passthrough kwargs
                gen_kwargs.update(kwargs or {})

                responses = await self.llm.generate(**gen_kwargs)
            finally:
                heartbeat_running = False
                try:
                    hb_task.cancel()
                except Exception:
                    pass

            full_response = ""
            for response in responses:
                content = response.content
                if not content:
                    continue
                if isinstance(content, str):
                    full_response += content

            if not full_response and streamed_chunks:
                full_response = "".join(streamed_chunks)

            # If the underlying LLM didn't support streaming callbacks, emit the
            # final response in chunks so clients still observe stream_chunk events
            if not used_stream_handler and full_response:
                # Simple chunker by characters; tune size for reasonable granularity
                chunk_size = 200
                for i in range(0, len(full_response), chunk_size):
                    await _emit("LLM response", full_response[i:i + chunk_size])

            return full_response

        except Exception as e:
            error_msg = f"Error processing financial analysis: {str(e)}"
            await _emit("error", error_msg)
            return error_msg

    async def _async_stream_wrapper(self, stream):
        """Convert synchronous stream to async generator."""
        import asyncio
        import concurrent.futures

        def get_next_chunk():
            try:
                return next(stream)
            except StopIteration:
                return None

        with concurrent.futures.ThreadPoolExecutor() as executor:
            while True:
                try:
                    chunk = await asyncio.get_event_loop().run_in_executor(
                        executor, get_next_chunk
                    )
                    if chunk is None:
                        break
                    yield chunk
                except Exception:
                    break


class StreamingVLLMAugmentedLLM(VLLMAugmentedLLM):
    """Custom VLLMAugmentedLLM that intercepts and streams tool calls in real-time."""

    def __init__(self, *args, tool_stream_callback=None, **kwargs):
        """Initialize with optional tool streaming callback."""
        super().__init__(*args, **kwargs)
        self.tool_stream_callback = tool_stream_callback

    async def select_model(self, request_params=None):
        """Override model selection to ensure we use the VLLM model."""
        # Always return the VLLM model
        return "Qwen/Qwen3-32B"

    async def call_tool(
        self,
        request: CallToolRequest,
        tool_call_id: str | None = None,
    ):
        """Override call_tool to intercept and stream tool calls."""
        tool_name = request.params.name
        tool_args = request.params.arguments

        # Stream tool call start
        if self.tool_stream_callback:
            await self._stream_tool_event({
                "type": "tool_call_start",
                "tool_name": tool_name,
                "args": tool_args,
                "tool_call_id": tool_call_id,
                "timestamp": datetime.now().isoformat()
            })

        start_time = datetime.now()

        try:
            # Execute the actual tool call
            result = await super().call_tool(request, tool_call_id)

            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call result
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_result",
                    "tool_name": tool_name,
                    "result": self._serialize_tool_result(result),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call error
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_error",
                    "tool_name": tool_name,
                    "error": str(e),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            raise

    async def execute_tool_call(self, tool_call):
        """Override execute_tool_call to intercept tool calls at the execution level."""
        tool_name = tool_call.function.name
        tool_args_str = tool_call.function.arguments
        tool_call_id = tool_call.id

        # Parse tool arguments
        tool_args = {}
        try:
            if tool_args_str:
                import json
                tool_args = json.loads(tool_args_str)
        except json.JSONDecodeError:
            tool_args = {"raw_arguments": tool_args_str}

        # Stream tool call start
        if self.tool_stream_callback:
            await self._stream_tool_event({
                "type": "tool_call_start",
                "tool_name": tool_name,
                "args": tool_args,
                "tool_call_id": tool_call_id,
                "timestamp": datetime.now().isoformat()
            })

        start_time = datetime.now()

        try:
            # Execute the actual tool call via parent method
            result = await super().execute_tool_call(tool_call)

            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call result
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_result",
                    "tool_name": tool_name,
                    "result": self._serialize_tool_message(result),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call error
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_error",
                    "tool_name": tool_name,
                    "error": str(e),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            raise

    async def _stream_tool_event(self, event_data):
        """Stream tool event via callback."""
        if self.tool_stream_callback:
            if asyncio.iscoroutinefunction(self.tool_stream_callback):
                await self.tool_stream_callback(event_data)
            else:
                self.tool_stream_callback(event_data)

    def _serialize_tool_message(self, tool_message):
        """Serialize tool message result for streaming."""
        try:
            if tool_message is None:
                return {"content": ["No content"], "is_error": False}

            if hasattr(tool_message, 'content'):
                return {
                    "content": [str(tool_message.content)],
                    "is_error": False,
                    "tool_call_id": getattr(tool_message, 'tool_call_id', None)
                }
            else:
                return {"content": [str(tool_message)], "is_error": False}
        except Exception as e:
            return {"content": [f"Error serializing tool message: {str(e)}"], "is_error": True}

    def _serialize_tool_result(self, result):
        """Serialize tool result for streaming."""
        try:
            if hasattr(result, 'content') and result.content:
                # Extract text content from MCP result
                content_texts = []
                for content in result.content:
                    if hasattr(content, 'text'):
                        content_texts.append(content.text)
                    elif hasattr(content, 'type') and content.type == 'text':
                        content_texts.append(str(content))

                return {
                    "content": content_texts,
                    "is_error": getattr(result, 'isError', False)
                }
            else:
                return {"content": [str(result)], "is_error": False}
        except Exception as e:
            return {"content": [f"Error serializing result: {str(e)}"], "is_error": True}


class MCPToolStreamingWrapper:
    """Wrapper for MCP agents that provides real-time streaming of tool calls."""

    def __init__(self, agent: Any, stream_callback: Callable[[Dict[str, Any]], None]):
        """
        Initialize MCP tool streaming wrapper.

        Args:
            agent: MCP agent with tool calling capabilities
            stream_callback: Callback function for streaming messages (can be async)
        """
        self.agent = agent
        self.stream_callback = stream_callback

    async def call_tool_streaming(
        self,
        tool_name: str,
        tool_args: Dict[str, Any]
    ) -> Any:
        """
        Call a tool with real-time streaming of parameters and results.

        Args:
            tool_name: Name of the tool to call
            tool_args: Arguments to pass to the tool

        Returns:
            Tool execution result
        """
        try:
            # Stream tool call start
            if self.stream_callback:
                if asyncio.iscoroutinefunction(self.stream_callback):
                    await self.stream_callback({
                        "type": "tool_call_start",
                        "tool_name": tool_name,
                        "args": tool_args,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    self.stream_callback({
                        "type": "tool_call_start",
                        "tool_name": tool_name,
                        "args": tool_args,
                        "timestamp": datetime.now().isoformat()
                    })

            # Execute the actual tool call
            result = await self.agent.call_tool(tool_name, tool_args)

            # Stream tool call result
            if self.stream_callback:
                if asyncio.iscoroutinefunction(self.stream_callback):
                    await self.stream_callback({
                        "type": "tool_call_result",
                        "tool_name": tool_name,
                        "result": result,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    self.stream_callback({
                        "type": "tool_call_result",
                        "tool_name": tool_name,
                        "result": result,
                        "timestamp": datetime.now().isoformat()
                    })

            return result

        except Exception as e:
            # Stream tool call error
            if self.stream_callback:
                if asyncio.iscoroutinefunction(self.stream_callback):
                    await self.stream_callback({
                        "type": "tool_call_error",
                        "tool_name": tool_name,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    self.stream_callback({
                        "type": "tool_call_error",
                        "tool_name": tool_name,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })

            # Re-raise the exception to maintain error handling behavior
            raise


class FinancialSession:
    """Represents a financial analysis session with specialized agent integration."""

    def __init__(self, user_id: str, session_id: str, session_type: SessionType):
        self.user_id = user_id
        self.session_id = session_id
        self.session_type = session_type
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.message_history = []
        self.company_name = None

        # MCP agent components
        self.mcp_app: Optional[MCPApp] = None
        self.agent_app = None

        # Specialized agents based on session type
        self.basic_agent = None
        self.research_agent = None
        self.analyst_agent = None
        self.report_writer = None
        self.orchestrator = None

        # LLM components
        self.llm = None
        self.streaming_llm = None

        # MCP streaming components
        self.mcp_streaming_wrapper = None

        # Early stream callback for VLLM response interception
        self.early_stream_callback = None

    async def initialize(self, company_name: str):
        """Initialize the financial analysis session with appropriate agents."""
        try:
            self.company_name = company_name
            
            # Create MCP app for this session
            self.mcp_app = MCPApp(name=f"financial_websocket_{self.session_type.value}_{self.user_id}")

            # Initialize the MCP app
            await self.mcp_app.initialize()
            self.agent_app = self.mcp_app

            # Get context and logger
            context = self.agent_app.context
            logger = self.agent_app.logger

            # Add current directory to filesystem server args
            context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])

            # Initialize agents based on session type
            await self._initialize_agents(company_name, logger)

            logger.info(f"Financial session initialized for user {self.user_id}, type: {self.session_type.value}")

        except Exception as e:
            if self.mcp_app:
                await self.mcp_app.cleanup()
            raise e

    async def _initialize_agents(self, company_name: str, logger):
        """Initialize appropriate agents based on session type."""

        if self.session_type == SessionType.BASIC:
            # Basic session with general purpose agent
            self.basic_agent = Agent(
                name="basic_assistant",
                instruction="""You are a helpful AI assistant with access to various tools.
                You can help with file operations, web searches, and general tasks.
                Be helpful, accurate, and provide clear responses."""
            )
            await self.basic_agent.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.basic_agent,
                context=self.basic_agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.basic_agent,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper and early stream callback
            self.streaming_llm = StreamingVLLMLLM(
                self.llm,
                mcp_streaming_wrapper=self.mcp_streaming_wrapper,
                early_stream_callback=self.early_stream_callback
            )

        elif self.session_type == SessionType.RESEARCH:
            # Research-only session
            self.research_agent = create_research_agent(company_name)
            await self.research_agent.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.research_agent,
                context=self.research_agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.research_agent,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper and early stream callback
            self.streaming_llm = StreamingVLLMLLM(
                self.llm,
                mcp_streaming_wrapper=self.mcp_streaming_wrapper,
                early_stream_callback=self.early_stream_callback
            )

        elif self.session_type == SessionType.ANALYZE:
            # Analysis-only session
            self.analyst_agent = create_analyst_agent(company_name)
            await self.analyst_agent.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.analyst_agent,
                context=self.analyst_agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.analyst_agent,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper and early stream callback
            self.streaming_llm = StreamingVLLMLLM(
                self.llm,
                mcp_streaming_wrapper=self.mcp_streaming_wrapper,
                early_stream_callback=self.early_stream_callback
            )

        elif self.session_type == SessionType.REPORT:
            # Report generation session
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{company_name.lower().replace(' ', '_')}_report_{timestamp}.md"
            output_path = os.path.join("company_reports", output_file)

            self.report_writer = create_report_writer(company_name, output_path)
            await self.report_writer.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.report_writer,
                context=self.report_writer.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.report_writer,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper and early stream callback
            self.streaming_llm = StreamingVLLMLLM(
                self.llm,
                mcp_streaming_wrapper=self.mcp_streaming_wrapper,
                early_stream_callback=self.early_stream_callback
            )

        elif self.session_type == SessionType.FULL_ANALYSIS:
            # Full analysis with orchestrator
            await self._initialize_full_analysis(company_name, logger)
            
        elif self.session_type == SessionType.ORCHESTRATION:
            # Orchestration session - uses global orchestration runner
            # No specific agents needed as orchestration runner manages them
            self.company_name = company_name
            # Use a basic agent for orchestration coordination if needed
            self.basic_agent = Agent(
                name="orchestration_coordinator",
                instruction=f"""You are an orchestration coordinator for financial analysis of {company_name}.
                You help coordinate complex multi-agent workflows and can provide updates on orchestration progress.
                Be helpful and provide clear status updates during orchestration workflows."""
            )
            await self.basic_agent.__aenter__()
            
        elif self.session_type == SessionType.MYSQL:
            # MySQL database analysis session (use orchestrator agent)
            self.mysql_orchestrator_agent = create_mysql_orchestrator_agent(
                company_name,
                tool_stream_callback=self._default_stream_callback
            )
            self.company_name = company_name
            
        elif self.session_type == SessionType.STORAGE_ANALYZER:
            # Storage analyzer session
            # Pass default stream callback so MCP tool events can be forwarded
            self.storage_analyzer_agent = create_shortage_analyzer_agent(
                company_name,
                tool_stream_callback=self._default_stream_callback
            )
            await self.storage_analyzer_agent.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.storage_analyzer_agent,
                context=self.storage_analyzer_agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.storage_analyzer_agent,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper and early stream callback
            self.streaming_llm = StreamingVLLMLLM(
                self.llm,
                mcp_streaming_wrapper=self.mcp_streaming_wrapper,
                early_stream_callback=self.early_stream_callback
            )
            
        elif self.session_type == SessionType.ALERT_MANAGER:
            # Alert manager session
            # Pass default stream callback so notification tool events can be forwarded
            self.alert_manager_agent = create_alert_manager_agent(
                company_name,
                tool_stream_callback=self._default_stream_callback
            )
            await self.alert_manager_agent.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.alert_manager_agent,
                context=self.alert_manager_agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.alert_manager_agent,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper and early stream callback
            self.streaming_llm = StreamingVLLMLLM(
                self.llm,
                mcp_streaming_wrapper=self.mcp_streaming_wrapper,
                early_stream_callback=self.early_stream_callback
            )

    async def _initialize_full_analysis(self, company_name: str, logger):
        """Initialize full analysis session with orchestrator."""
        # Create all agents
        self.research_agent = create_research_agent(company_name)
        
        # Research evaluator
        research_evaluator = Agent(
            name="research_evaluator",
            instruction=f"""You are an expert research evaluator specializing in financial data quality.
            
            Evaluate the research data on {company_name} based on these criteria:
            
            1. Accuracy: Are facts properly cited with source URLs? Are numbers precise?
            2. Completeness: Is all required information present? (stock price, earnings data, recent news)
            3. Specificity: Are exact figures provided rather than generalizations?
            4. Clarity: Is the information organized and easy to understand?
            
            For each criterion, provide a rating:
            - EXCELLENT: Exceeds requirements, highly reliable
            - GOOD: Meets all requirements, reliable
            - FAIR: Missing some elements but usable
            - POOR: Missing critical information, not usable
            
            Provide an overall quality rating and specific feedback on what needs improvement.
            If any critical financial data is missing (stock price, earnings figures), the overall
            rating should not exceed FAIR.""",
        )

        # Create the research quality controller - use VLLM
        research_quality_controller = EvaluatorOptimizerLLM(
            optimizer=self.research_agent,
            evaluator=research_evaluator,
            llm_factory=VLLMAugmentedLLM,
            min_rating=QualityRating.EXCELLENT,
        )

        self.analyst_agent = create_analyst_agent(company_name)

        # Create report writer with timestamped output
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{company_name.lower().replace(' ', '_')}_report_{timestamp}.md"
        output_path = os.path.join("company_reports", output_file)
        self.report_writer = create_report_writer(company_name, output_path)

        # Create orchestrator - use VLLM
        self.orchestrator = Orchestrator(
            llm_factory=VLLMAugmentedLLM,
            available_agents=[
                research_quality_controller,
                self.analyst_agent,
                self.report_writer,
            ],
            plan_type="full",
        )

        # Create streaming LLM for full analysis
        try:
            # Try to get LLM from orchestrator or create one
            if hasattr(self.orchestrator, 'llm') and self.orchestrator.llm:
                self.llm = self.orchestrator.llm
            else:
                # Create a VLLM instance for streaming
                async with create_research_agent(company_name) as temp_agent:
                    self.llm = await temp_agent.attach_llm(VLLMAugmentedLLM)

            self.streaming_llm = StreamingVLLMLLM(self.llm)
        except Exception as e:
            logger.warning(f"Failed to create streaming LLM for full analysis: {e}")
            self.streaming_llm = None

    async def _default_stream_callback(self, message: Dict[str, Any]):
        """Default stream callback for MCP tool streaming (no-op)."""
        # This is a placeholder callback for when no specific callback is provided
        # In practice, this will be overridden by the WebSocket streaming callback
        pass

    def set_mcp_stream_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set the stream callback for MCP tool streaming."""
        # Store the callback in the session for orchestration use
        self._tool_stream_callback = callback
        logger.info(f"Session {self.session_id}: Tool streaming callback set")

        # Update wrapper callback if present
        if self.mcp_streaming_wrapper:
            self.mcp_streaming_wrapper.stream_callback = callback
        # Update LLM callback if present
        if hasattr(self, 'llm') and hasattr(self.llm, 'tool_stream_callback'):
            self.llm.tool_stream_callback = callback
        # Update specialized agents that support tool streaming
        if hasattr(self, 'mysql_orchestrator_agent') and self.mysql_orchestrator_agent:
            if hasattr(self.mysql_orchestrator_agent, 'tool_stream_callback'):
                self.mysql_orchestrator_agent.tool_stream_callback = callback
        if hasattr(self, 'storage_analyzer_agent') and self.storage_analyzer_agent and hasattr(self.storage_analyzer_agent, 'tool_stream_callback'):
            self.storage_analyzer_agent.tool_stream_callback = callback
        if hasattr(self, 'alert_manager_agent') and self.alert_manager_agent and hasattr(self.alert_manager_agent, 'tool_stream_callback'):
            self.alert_manager_agent.tool_stream_callback = callback

        # Propagate to orchestration runner agents so pattern-based workflows stream tool events
        runner = getattr(self.session_manager, 'orchestration_runner', None)
        if runner:
            for agent_attr in ("mysql_agent", "shortage_agent", "alert_agent"):
                agent_obj = getattr(runner, agent_attr, None)
                if agent_obj and hasattr(agent_obj, 'tool_stream_callback'):
                    agent_obj.tool_stream_callback = callback

    def set_early_stream_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set the early stream callback for VLLM response interception."""
        self.early_stream_callback = callback
        logger.info(f"Session {self.session_id}: Early stream callback set for VLLM interception")

        # Update StreamingVLLMLLM early stream callback if present
        if self.streaming_llm:
            self.streaming_llm.early_stream_callback = callback
            if self.streaming_llm.response_interceptor:
                self.streaming_llm.response_interceptor.early_stream_callback = callback

    async def process_message(self, message: str) -> str:
        """Process a user message through the appropriate financial analysis workflow."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process based on session type
            response = await self._process_by_type(message)

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing financial analysis: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            return error_msg

    async def process_message_streaming(
        self,
        message: str,
        stream_callback: Callable[[Any], Any]
    ) -> str:
        """Process a user message with streaming response."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process based on session type with streaming
            response = await self._process_by_type_streaming(message, stream_callback)

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing financial analysis: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            await stream_callback(error_msg)
            return error_msg

    async def _process_by_type(self, message: str) -> str:
        """Process message based on session type."""

        if self.session_type == SessionType.BASIC:
            if not self.llm:
                return "Error: Basic agent not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.RESEARCH:
            if not self.llm:
                return "Error: Research agent not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.ANALYZE:
            if not self.llm:
                return "Error: Analyst agent not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.REPORT:
            if not self.llm:
                return "Error: Report writer not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.FULL_ANALYSIS:
            if not self.orchestrator:
                return "Error: Orchestrator not initialized"

            # Create comprehensive analysis task
            task = f"""Create a high-quality stock analysis report for {self.company_name} by following these steps:

            1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality
               financial data about {self.company_name}. This component will automatically evaluate
               and improve the research until it reaches EXCELLENT quality.

               Ask for:
               - Current stock price and recent movement
               - Latest quarterly earnings results and performance vs expectations
               - Recent news and developments

            2. Use the financial_analyst to analyze this research data and identify key insights.

            3. Use the report_writer to create a comprehensive stock report.

            The final report should be professional, fact-based, and include all relevant financial information.

            User request: {message}"""

            return await self.orchestrator.generate_str(
                message=task,
                request_params=RequestParams(model="Qwen/Qwen3-32B")
            )
            
        elif self.session_type == SessionType.ORCHESTRATION:
            # Use the global orchestration runner from session manager
            if not hasattr(self, 'session_manager') or not self.session_manager.orchestration_runner:
                return "Error: Orchestration runner not available"
            
            try:
                # Execute the financial query using orchestration runner
                result = await self.session_manager.orchestration_runner.execute_financial_query(
                    query=message,
                    execution_mode="orchestrator_based"  # Default to orchestrator-based execution
                )
                
                if result.get("success", False):
                    return result.get("result", "Orchestration completed successfully")
                else:
                    error_msg = result.get("error", "Unknown orchestration error")
                    return f"Orchestration failed: {error_msg}"
                    
            except Exception as e:
                return f"Error during orchestration: {str(e)}"
        
        elif self.session_type == SessionType.MYSQL:
            # MySQL database analysis without streaming
            if not self.llm:
                return "Error: MySQL agent not initialized"
            return await self.llm.generate_str(message=message)
            
        elif self.session_type == SessionType.STORAGE_ANALYZER:
            # Storage analyzer without streaming
            if not self.llm:
                return "Error: Storage analyzer agent not initialized"
            return await self.llm.generate_str(message=message)
            
        elif self.session_type == SessionType.ALERT_MANAGER:
            # Alert manager without streaming
            if not self.llm:
                return "Error: Alert manager agent not initialized"
            return await self.llm.generate_str(message=message)

    async def _process_by_type_streaming(
        self,
        message: str,
        stream_callback: Callable[[Any], Any]
    ) -> str:
        """Process message based on session type with streaming responses."""

        if self.session_type == SessionType.BASIC:
            if not self.streaming_llm:
                error_msg = "Error: Basic agent streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.RESEARCH:
            if not self.streaming_llm:
                error_msg = "Error: Research agent streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.ANALYZE:
            if not self.streaming_llm:
                error_msg = "Error: Analyst agent streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.REPORT:
            if not self.streaming_llm:
                error_msg = "Error: Report writer streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.FULL_ANALYSIS:
            if not self.orchestrator or not self.streaming_llm:
                error_msg = "Error: Full analysis streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            # For full analysis, we'll use a combination of streaming and non-streaming
            # First, send a message that we're starting the analysis
            await stream_callback("Starting comprehensive financial analysis for " + self.company_name + "...\n\n")

            # Create comprehensive analysis task
            task = f"""Create a high-quality stock analysis report for {self.company_name} by following these steps:

            1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality
               financial data about {self.company_name}. This component will automatically evaluate
               and improve the research until it reaches EXCELLENT quality.

               Ask for:
               - Current stock price and recent movement
               - Latest quarterly earnings results and performance vs expectations
               - Recent news and developments

            2. Use the financial_analyst to analyze this research data and identify key insights.

            3. Use the report_writer to create a comprehensive stock report.

            The final report should be professional, fact-based, and include all relevant financial information.

            User request: {message}"""

            # For orchestrator, we can't easily stream, so we'll provide progress updates
            await stream_callback("Researching financial data...\n")

            # Use the orchestrator (non-streaming)
            result = await self.orchestrator.generate_str(
                message=task,
                request_params=RequestParams(model="Qwen/Qwen3-32B")
            )

            # Send the final result
            await stream_callback("\n\nFinal Analysis Report:\n\n")
            await stream_callback(result)

            return result
            
        elif self.session_type == SessionType.ORCHESTRATION:
            # Use the global orchestration runner with streaming
            if not hasattr(self, 'session_manager') or not self.session_manager.orchestration_runner:
                error_msg = "Error: Orchestration runner not available"
                await stream_callback(error_msg)
                return error_msg
            
            try:
                # Determine execution mode (default to orchestrator_based for orchestration sessions)
                execution_mode = getattr(self, 'execution_mode', 'orchestrator_based')

                llm_chunk_index = 0
                llm_total_chars = 0

                async def _orchestration_stream_adapter(message: Any, label: str = "workflow") -> None:
                    nonlocal llm_chunk_index, llm_total_chars
                    payload: Dict[str, Any]
                    if isinstance(message, dict):
                        payload = dict(message)
                        if "label" not in payload:
                            payload = {
                                **payload,
                                "label": label,
                                "timestamp": payload.get("timestamp") or datetime.utcnow().isoformat() + "Z",
                            }
                        elif "timestamp" not in payload:
                            payload = {
                                **payload,
                                "timestamp": datetime.utcnow().isoformat() + "Z",
                            }
                        payload.setdefault("source", "workflow_executor")
                        payload_label = str(payload.get("label") or label)
                        content_value = payload.get("content", "")
                        if (
                            payload_label.lower() == "workflow"
                            and isinstance(content_value, str)
                        ):
                            normalized_text = content_value.lstrip()
                            # Emoji characters removed - no longer checking for emoji prefixes
                            # if normalized_text.startswith("💭") or normalized_text.startswith("📄"):
                            #     payload_label = "LLM response"
                            #     payload["label"] = payload_label
                            #     payload["type"] = "llm_response"
                        if payload_label.lower() == "llm response":
                            if isinstance(content_value, str):
                                chunk_text = content_value
                            else:
                                chunk_text = json.dumps(content_value, ensure_ascii=False)
                            llm_chunk_index += 1
                            llm_total_chars += len(chunk_text)
                            payload.setdefault("chunk_index", llm_chunk_index)
                            payload.setdefault(
                                "metrics",
                                {
                                    "chunk_chars": len(chunk_text),
                                    "cumulative_chars": llm_total_chars,
                                },
                            )
                    else:
                        text_content = str(message)
                        normalized = text_content.lstrip()
                        inferred_label = label
                        # Emoji characters removed - no longer checking for emoji prefixes
                        # if normalized.startswith("💭") or normalized.startswith("📄"):
                        #     inferred_label = "LLM response"
                        payload = {
                            "type": inferred_label.lower().replace(" ", "_"),
                            "label": inferred_label,
                            "content": text_content,
                            "timestamp": datetime.utcnow().isoformat() + "Z",
                            "source": "workflow_executor",
                        }
                        if inferred_label.lower() == "llm response":
                            llm_chunk_index += 1
                            llm_total_chars += len(text_content)
                            payload["chunk_index"] = llm_chunk_index
                            payload["metrics"] = {
                                "chunk_chars": len(text_content),
                                "cumulative_chars": llm_total_chars,
                            }

                    callback_result = stream_callback(payload)
                    if inspect.isawaitable(callback_result):
                        await callback_result

                # REMOVED: Initial orchestration workflow message per user request
                # await _orchestration_stream_adapter(
                #     f"Starting orchestrated financial analysis (Mode: {execution_mode})...\n\n",
                #     label="workflow",
                # )

                # Get the tool streaming callback from the session
                tool_stream_callback = getattr(self, '_tool_stream_callback', None)

                # Enhanced logging for tool streaming diagnostics
                if tool_stream_callback:
                    logger.info(f"Session {self.session_id}: Tool streaming callback available for orchestration")
                else:
                    logger.warning(f"Session {self.session_id}: No tool streaming callback available for orchestration")

                # Execute the financial query using orchestration runner with streaming
                result = await self.session_manager.orchestration_runner.execute_financial_query(
                    query=message,
                    execution_mode=execution_mode,
                    stream_callback=_orchestration_stream_adapter,
                    tool_stream_callback=tool_stream_callback,
                    early_stream_callback=self.early_stream_callback,
                )
                
                # REMOVED: Stream final results and metadata per user request
                if result.get("success", False):
                    # REMOVED: Stream execution metadata
                    # if "execution_time" in result:
                    #     await _orchestration_stream_adapter(
                    #         f"Execution time: {result['execution_time']:.2f} seconds\n",
                    #         label="workflow",
                    #     )
                    # if "query_analysis" in result:
                    #     query_analysis = result["query_analysis"]
                    #     await _orchestration_stream_adapter(
                    #         f"Query type: {query_analysis.get('type', 'unknown')}\n",
                    #         label="workflow",
                    #     )
                    #     await _orchestration_stream_adapter(
                    #         f"Confidence: {query_analysis.get('confidence', 0):.2f}\n",
                    #         label="workflow",
                    #     )
                    #     await _orchestration_stream_adapter(
                    #         f"Workflow pattern: {query_analysis.get('workflow_pattern', 'unknown')}\n\n",
                    #         label="workflow",
                    #     )

                    # REMOVED: Stream the main result
                    main_result = result.get("result", "Orchestration completed successfully")
                    # await _orchestration_stream_adapter("Final Results:\n", label="workflow")
                    # await _orchestration_stream_adapter("=" * 50 + "\n", label="workflow")
                    # await _orchestration_stream_adapter(main_result, label="workflow")
                    
                    return main_result
                else:
                    error_msg = result.get("error", "Unknown orchestration error")
                    await _orchestration_stream_adapter(
                        f"Orchestration failed: {error_msg}\n",
                        label="workflow",
                    )
                    return f"Orchestration failed: {error_msg}"

            except Exception as e:
                error_msg = f"Error during orchestration: {str(e)}"
                await _orchestration_stream_adapter(f"{error_msg}\n", label="workflow")
                return error_msg

        elif self.session_type == SessionType.DEMO:
            # Handle demo scenarios with streaming support
            if not hasattr(self, 'session_manager') or not self.session_manager.orchestration_runner:
                error_msg = "Error: Orchestration runner not available for demo scenarios"
                await stream_callback(error_msg)
                return error_msg
            
            try:
                # Parse scenario number from message (expecting format like "demo:1" or just "1")
                scenario_num = 1  # Default scenario
                if message.startswith("demo:"):
                    try:
                        scenario_num = int(message.split(":")[1])
                    except (IndexError, ValueError):
                        pass
                elif message.isdigit():
                    try:
                        scenario_num = int(message)
                    except ValueError:
                        pass
                
                # Define demo scenarios (same as in execute_demo_scenario)
                scenarios = {
                    1: {
                        "name": "CRITICAL Database Shortage Analysis with Customer Alert",
                        "query": "CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION.",
                        "description": "Triggers MySQL → Shortage Analysis → Alert Manager pipeline"
                    },
                    2: {
                        "name": "Supplier Risk Alert with SLA Breach Notification",
                        "query": "Query database for MetaMind Technology Co., Ltd. inventory levels DEP2004IC001/MM2004IC001. Calculate supplier risk for 500 CPUs, 200 GPUs delivery. If shortage detected, ESCALATE IMMEDIATELY to management and SEND CRITICAL ALERT to customer regarding SLA BREACH risk.",
                        "description": "Tests database → analysis → customer notification workflow"
                    },
                    3: {
                        "name": "URGENT Customer Priority Alert Analysis",
                        "query": "URGENT: Query database for WO-202506001 component availability (HCS500D001, MM2004IC001, ATR6G00801). Calculate shortage risk and if CRITICAL, send IMMEDIATE CUSTOMER NOTIFICATION to Tech Pioneer Co., Ltd. with Net 30 payment escalation.",
                        "description": "Database check → shortage analysis → priority customer alerts"
                    },
                    4: {
                        "name": "Multi-Order Critical Alert Orchestration",
                        "query": "CRITICAL MULTI-ORDER ANALYSIS: Query database for WO-202506001/002/003 inventory (DEP2004IC001, MM2004IC001, ATR6G00801, MS300PC801). Calculate weighted shortage indices and TRIGGER IMMEDIATE ALERTS for any CRITICAL shortages with CUSTOMER ESCALATION and management notification.",
                        "description": "Full pipeline: database → analysis → alert orchestration"
                    }
                }

                if scenario_num not in scenarios:
                    error_msg = f"Invalid demo scenario number: {scenario_num}. Must be 1-4."
                    await stream_callback(error_msg)
                    return error_msg

                scenario = scenarios[scenario_num]

                # Stream demo scenario information
                await stream_callback(f"🎭 Demo Scenario {scenario_num}: {scenario['name']}\n")
                await stream_callback(f"Description: {scenario['description']}\n")
                await stream_callback(f"Query: {scenario['query']}\n\n")
                await stream_callback("Executing orchestration pipeline...\n\n")

                # Get the tool streaming callback from the session
                tool_stream_callback = getattr(self, '_tool_stream_callback', None)

                # Enhanced logging for demo scenario tool streaming
                if tool_stream_callback:
                    logger.info(f"Demo scenario {scenario_num}: Tool streaming callback available")
                else:
                    logger.warning(f"Demo scenario {scenario_num}: No tool streaming callback available")

                # Execute the scenario using orchestration runner
                result = await self.session_manager.orchestration_runner.execute_financial_query(
                    query=scenario['query'],
                    execution_mode="pattern_based",
                    stream_callback=stream_callback,
                    tool_stream_callback=tool_stream_callback,
                    early_stream_callback=self.early_stream_callback,
                )

                # Stream results
                if result and result.get('success', False):
                    await stream_callback("Demo scenario completed successfully!\n\n")

                    # Stream detailed results
                    execution_time = result.get('execution_time', 0)
                    workflow_id = result.get('workflow_id', 'unknown')
                    
                    await stream_callback(f"Execution Time: {execution_time:.2f}s\n")
                    await stream_callback(f"Workflow ID: {workflow_id}\n")
                    
                    query_analysis = result.get('query_analysis', {})
                    await stream_callback(f"Query Type: {query_analysis.get('type', 'unknown')}\n")
                    await stream_callback(f"Confidence: {query_analysis.get('confidence', 0):.2f}\n")
                    await stream_callback(f"Workflow Pattern: {query_analysis.get('workflow_pattern', 'unknown')}\n\n")
                    
                    # Stream agent execution results
                    await stream_callback("Agent Execution Results:\n")
                    await stream_callback("-" * 30 + "\n")
                    
                    if result.get('mysql_analysis'):
                        mysql_analysis = result['mysql_analysis']
                        mysql_success = mysql_analysis.get('success', False)
                        await stream_callback(f"MySQL Analysis: {'SUCCESS' if mysql_success else 'FAILED'}\n")

                    if result.get('shortage_analysis'):
                        shortage_analysis = result['shortage_analysis']
                        shortage_success = shortage_analysis.get('success', False)
                        shortage_risk = shortage_analysis.get('risk_level', 'unknown')
                        await stream_callback(f"Shortage Analysis: {'SUCCESS' if shortage_success else 'FAILED'} (Risk: {shortage_risk})\n")

                    if result.get('alert_management'):
                        alert_management = result['alert_management']
                        alert_success = alert_management.get('success', False)
                        alerts_sent = len(alert_management.get('alerts_sent', []))
                        await stream_callback(f"Alert Management: {'SUCCESS' if alert_success else 'FAILED'} ({alerts_sent} alerts)\n")
                    
                    # Stream final response if available
                    final_response = result.get('final_response', 'Demo scenario completed')
                    await stream_callback(f"\nFinal Response:\n{final_response}")
                    
                    return final_response
                else:
                    error_msg = result.get('error', 'Unknown demo scenario error') if result else 'No result returned'
                    await stream_callback(f"Demo scenario failed: {error_msg}\n")
                    return f"Demo scenario failed: {error_msg}"

            except Exception as e:
                error_msg = f"Error during demo scenario execution: {str(e)}"
                await stream_callback(f"{error_msg}\n")
                return error_msg
                
        elif self.session_type == SessionType.MYSQL:
            # MySQL database analysis with orchestrator agent
            if not hasattr(self, 'mysql_orchestrator_agent') or not self.mysql_orchestrator_agent:
                error_msg = "Error: MySQL orchestrator agent not initialized"
                await stream_callback(error_msg)
                return error_msg

            # Use the MySQL orchestrator agent
            try:
                process_query = getattr(self.mysql_orchestrator_agent, 'process_query', None)
                if not process_query:
                    error_msg = "Error: MySQL agent lacks process_query implementation"
                    await stream_callback(error_msg)
                    return error_msg

                result = await process_query(message, stream_callback=stream_callback)

                if isinstance(result, dict):
                    final_response = (
                        result.get('final_response')
                        or result.get('error')
                        or ""
                    )
                    if not final_response:
                        final_response = json.dumps(result, ensure_ascii=False)
                    return final_response

                return str(result)
            except Exception as e:
                error_msg = f"Error processing MySQL query: {str(e)}"
                await stream_callback(error_msg)
                return error_msg
            
        elif self.session_type == SessionType.STORAGE_ANALYZER:
            # Storage analyzer with streaming
            if not self.streaming_llm:
                error_msg = "Error: Storage analyzer agent streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )
            
        elif self.session_type == SessionType.ALERT_MANAGER:
            # Alert manager with streaming
            if not self.streaming_llm:
                error_msg = "Error: Alert manager agent streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

    async def cleanup(self):
        """Clean up the session resources."""
        try:
            if self.basic_agent:
                await self.basic_agent.__aexit__(None, None, None)
            if self.research_agent:
                await self.research_agent.__aexit__(None, None, None)
            if self.analyst_agent:
                await self.analyst_agent.__aexit__(None, None, None)
            if self.report_writer:
                await self.report_writer.__aexit__(None, None, None)
            if self.mcp_app:
                await self.mcp_app.cleanup()
            if hasattr(self, 'mysql_agent') and self.mysql_agent:
                await self.mysql_agent.__aexit__(None, None, None)
            if hasattr(self, 'storage_analyzer_agent') and self.storage_analyzer_agent:
                await self.storage_analyzer_agent.__aexit__(None, None, None)
            if hasattr(self, 'alert_manager_agent') and self.alert_manager_agent:
                await self.alert_manager_agent.__aexit__(None, None, None)
        except Exception as e:
            print(f"Error during session cleanup for user {self.user_id}: {e}")


class FinancialSessionManager:
    """Manages financial analysis sessions for the WebSocket server."""

    def __init__(self):
        self.sessions: Dict[str, FinancialSession] = {}
        self.cleanup_interval = 3600  # Clean up inactive sessions every hour
        self.max_inactive_time = 7200  # Remove sessions inactive for 2 hours
        self.orchestration_runner: Optional[OrchestrationRunner] = None
        self.agent_manager: Optional[AgentOrchestrationManager] = None
        self.server_manager = None  # Will be initialized at runtime to avoid circular imports
        self.logger = logging.getLogger(__name__)

    async def initialize(self):
        """Initialize the session manager with comprehensive orchestration support."""
        self.logger.info("=== Initializing Financial Session Manager with Orchestration ===")

        # Start cleanup task
        asyncio.create_task(self._cleanup_task())

        # Ensure output directory exists
        os.makedirs("company_reports", exist_ok=True)
        os.makedirs("logs", exist_ok=True)

        # Initialize comprehensive orchestration system
        await self._initialize_orchestration_system()

    async def _initialize_orchestration_system(self) -> bool:
        """Initialize the complete orchestration system with comprehensive setup."""
        try:
            # Try to import server manager at runtime to avoid circular imports
            # This is optional - if not available, we'll skip server health checks
            try:
                # Try importing from parent directory first
                import sys
                import os
                parent_dir = os.path.join(os.path.dirname(__file__), '..')
                if parent_dir not in sys.path:
                    sys.path.append(parent_dir)
                from main import DualServerManager
                self.server_manager = DualServerManager()

                # Step 1: Check server availability
                self.logger.info("Checking MCP server availability...")
                shortage_available, alert_available, mysql_available = await self.server_manager.check_all_servers_health()

                if not all([shortage_available, alert_available, mysql_available]):
                    missing_servers = []
                    if not shortage_available:
                        missing_servers.append("shortage-index (6970)")
                    if not alert_available:
                        missing_servers.append("alert-notification (6972)")
                    if not mysql_available:
                        missing_servers.append("mysql (8702)")

                    self.logger.error(f"Required MCP servers not available: {', '.join(missing_servers)}")
                    self.server_manager.display_startup_instructions(
                        shortage_needed=not shortage_available,
                        alert_needed=not alert_available,
                        mysql_needed=not mysql_available
                    )
                    return False

                self.logger.info("✓ All MCP servers are available")

            except ImportError as import_error:
                self.logger.warning(f"DualServerManager not available: {import_error}")
                self.logger.info("Skipping MCP server health checks - proceeding with orchestration initialization")
                self.server_manager = None

            # Step 2: Initialize MCP App context
            self.logger.info("Initializing MCP App context...")
            app = MCPApp(
                name="websocket_financial_analyzer",
                human_input_callback=None,
                settings=str(Path(__file__).parent / "mcp_agent.config.yaml")
            )

            async with app.run() as analyzer_app:
                context = analyzer_app.context
                self.logger.info("✓ MCP App context initialized")

                # Step 3: Create agents
                self.logger.info("Creating and initializing agents...")
                mysql_agent = create_mysql_orchestrator_agent()
                shortage_agent = create_shortage_analyzer_agent("WebSocketOrchestration")
                # Configure alert manager for WebSocket environment
                alert_agent = create_alert_manager_agent(
                    "WebSocketOrchestration",
                    alert_config={
                        "shortage_threshold": 0.55,
                        "enabled_channels": ["http"],
                    }
                )

                # Step 4: Initialize agents for MCP
                agents_to_init = [
                    ("MySQL", mysql_agent),
                    ("Shortage", shortage_agent),
                    ("Alert", alert_agent)
                ]

                for agent_name, agent in agents_to_init:
                    if hasattr(agent, 'initialize_llm'):
                        try:
                            await agent.initialize_llm()
                            self.logger.info(f"✓ {agent_name} agent LLM initialized")
                        except Exception as e:
                            self.logger.error(f"✗ {agent_name} agent initialization failed: {e}")
                            return False
                    else:
                        self.logger.warning(f"✗ {agent_name} agent does not support LLM initialization")

                # Step 5: Create comprehensive LLM factory with debug logging
                def llm_factory(agent: Agent) -> AugmentedLLM:
                    """Factory function to create LLM instances with comprehensive debug logging."""
                    from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM

                    llm_debug_logger.debug(f"🏭 Creating LLM instance for agent: {agent.name}")
                    llm_debug_logger.debug(f"📋 Agent type: {type(agent).__name__}")

                    try:
                        # Try to use the same LLM configuration as the agents
                        llm_instance = VLLMAugmentedLLM(agent=agent, context=context)

                        # Wrap the LLM to capture reasoning and responses
                        original_generate = llm_instance.generate
                        original_generate_str = llm_instance.generate_str

                        async def debug_generate(*args, **kwargs):
                            start_time = time.time()

                            # Extract input details for logging
                            input_query = ""
                            if args:
                                if hasattr(args[0], 'content'):
                                    input_query = str(args[0].content)
                                elif hasattr(args[0], 'messages'):
                                    input_query = str(args[0].messages)
                                else:
                                    input_query = str(args[0])

                            # Check for thinking mode in kwargs
                            enable_thinking = kwargs.get('enable_thinking', False)

                            log_llm_interaction(
                                "GENERATE_START",
                                agent.name,
                                query=input_query,
                                extra_data={
                                    "method": "generate",
                                    "args_count": len(args),
                                    "enable_thinking": enable_thinking,
                                    "kwargs_keys": list(kwargs.keys())
                                }
                            )

                            try:
                                result = await original_generate(*args, **kwargs)
                                execution_time = time.time() - start_time

                                # Extract comprehensive result information
                                reasoning = ""
                                thinking_content = ""
                                raw_completion = ""
                                response = ""

                                if result:
                                    # Try to extract reasoning from various possible attributes
                                    reasoning = getattr(result, 'reasoning', '') or getattr(result, 'chain_of_thought', '')
                                    thinking_content = getattr(result, 'thinking', '') or getattr(result, 'thought_process', '')
                                    raw_completion = getattr(result, 'raw_response', '') or getattr(result, 'completion', '')

                                    # Handle different result types
                                    if hasattr(result, 'content'):
                                        response = str(result.content)
                                    elif hasattr(result, 'message'):
                                        response = str(result.message)
                                    else:
                                        response = str(result)

                                log_llm_interaction(
                                    "GENERATE_COMPLETE",
                                    agent.name,
                                    response=response,
                                    reasoning=reasoning,
                                    thinking_content=thinking_content,
                                    raw_completion=raw_completion,
                                    execution_time=execution_time,
                                    extra_data={
                                        "result_type": type(result).__name__,
                                        "has_reasoning": bool(reasoning),
                                        "has_thinking": bool(thinking_content),
                                        "response_length": len(response) if response else 0
                                    }
                                )

                                return result
                            except Exception as e:
                                execution_time = time.time() - start_time
                                log_llm_interaction(
                                    "GENERATE_ERROR",
                                    agent.name,
                                    response=f"ERROR: {str(e)}",
                                    execution_time=execution_time,
                                    extra_data={
                                        "error_type": type(e).__name__,
                                        "error_details": str(e)
                                    }
                                )
                                raise

                        # Replace methods with debug versions
                        llm_instance.generate = debug_generate

                        llm_debug_logger.debug(f"LLM instance created successfully for {agent.name}")
                        return llm_instance

                    except Exception as e:
                        # Fallback to basic configuration
                        self.logger.warning(f"Using fallback LLM configuration for {agent.name}: {e}")
                        llm_debug_logger.debug(f"LLM factory fallback for {agent.name}: {str(e)}")
                        return VLLMAugmentedLLM(agent=agent, context=context)

                # Step 6: Create orchestration runner
                self.logger.info("Creating orchestration runner...")
                self.orchestration_runner = create_orchestration_runner(
                    mysql_agent=mysql_agent,
                    shortage_agent=shortage_agent,
                    alert_agent=alert_agent,
                    llm_factory=llm_factory,
                    persist_context=True
                )

                # Step 7: Create agent manager for interface testing
                self.agent_manager = AgentOrchestrationManager()
                self.agent_manager.register_agent("mysql_analyzer", mysql_agent)
                self.agent_manager.register_agent("shortage_analyzer", shortage_agent)
                self.agent_manager.register_agent("alert_manager", alert_agent)

                # Initialize agent interfaces
                await self.agent_manager.initialize_all_agents()

                self.logger.info("✓ Orchestration system initialized successfully")
                return True

        except Exception as e:
            self.logger.error(f"Failed to initialize orchestration system: {e}", exc_info=True)
            self.orchestration_runner = None
            self.agent_manager = None
            return False

    async def run_orchestration_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check of the orchestration system."""
        if not self.orchestration_runner:
            return {"status": "not_initialized", "error": "Orchestration system not initialized"}

        self.logger.info("Running orchestration system health check...")
        health = await self.orchestration_runner.health_check()

        # Display health status
        self.logger.info(f"System Status: {health['status']}")
        self.logger.info(f"Components: {len(health['components'])}")

        for component, status in health.get('components', {}).items():
            if isinstance(status, dict):
                comp_status = status.get('status', 'unknown')
                self.logger.info(f"  {component}: {comp_status}")
            else:
                self.logger.info(f"  {component}: {status}")

        if health['status'] != 'healthy':
            self.logger.warning("System health issues detected")
            if 'issues' in health:
                for issue in health['issues']:
                    self.logger.warning(f"  - {issue}")
        else:
            self.logger.info("All systems healthy")

        return health

    async def execute_demo_scenario(self, scenario_num: int, stream_callback: Optional[Callable[[Any], Any]] = None) -> Dict[str, Any]:
        """Execute a predefined demo scenario."""
        if not self.orchestration_runner:
            error_msg = "Orchestration system not initialized"
            if stream_callback:
                await stream_callback(f"Error: {error_msg}")
            return {"success": False, "error": error_msg}

        scenarios = {
            1: {
                "name": "CRITICAL Database Shortage Analysis with Customer Alert",
                "query": "CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION.",
                "description": "Triggers MySQL → Shortage Analysis → Alert Manager pipeline"
            },
            2: {
                "name": "Supplier Risk Alert with SLA Breach Notification",
                "query": "Query database for MetaMind Technology Co., Ltd. inventory levels DEP2004IC001/MM2004IC001. Calculate supplier risk for 500 CPUs, 200 GPUs delivery. If shortage detected, ESCALATE IMMEDIATELY to management and SEND CRITICAL ALERT to customer regarding SLA BREACH risk.",
                "description": "Tests database → analysis → customer notification workflow"
            },
            3: {
                "name": "URGENT Customer Priority Alert Analysis",
                "query": "URGENT: Query database for WO-202506001 component availability (HCS500D001, MM2004IC001, ATR6G00801). Calculate shortage risk and if CRITICAL, send IMMEDIATE CUSTOMER NOTIFICATION to Tech Pioneer Co., Ltd. with Net 30 payment escalation.",
                "description": "Database check → shortage analysis → priority customer alerts"
            },
            4: {
                "name": "Multi-Order Critical Alert Orchestration",
                "query": "CRITICAL MULTI-ORDER ANALYSIS: Query database for WO-202506001/002/003 inventory (DEP2004IC001, MM2004IC001, ATR6G00801, MS300PC801). Calculate weighted shortage indices and TRIGGER IMMEDIATE ALERTS for any CRITICAL shortages with CUSTOMER ESCALATION and management notification.",
                "description": "Full pipeline: database → analysis → alert orchestration"
            }
        }

        if scenario_num not in scenarios:
            error_msg = f"Invalid scenario number: {scenario_num}. Must be 1-4."
            if stream_callback:
                await stream_callback(f"Error: {error_msg}")
            return {"success": False, "error": error_msg}

        scenario = scenarios[scenario_num]

        if stream_callback:
            await stream_callback(f"--- Demo Scenario {scenario_num}: {scenario['name']} ---\n")
            await stream_callback(f"Description: {scenario['description']}\n")
            await stream_callback(f"Query: {scenario['query']}\n\n")

        self.logger.info(f"Executing demo scenario {scenario_num}: {scenario['name']}")

        try:
            # Log orchestration start with full details
            log_llm_interaction(
                "ORCHESTRATION_START",
                "OrchestrationRunner",
                query=scenario['query'],
                extra_data={
                    "scenario_num": scenario_num,
                    "scenario_name": scenario['name'],
                    "execution_mode": "pattern_based"
                }
            )

            orchestration_start_time = time.time()

            # Execute using pattern-based orchestration
            result = await self.orchestration_runner.execute_financial_query(
                query=scenario['query'],
                execution_mode="pattern_based"
            )

            orchestration_execution_time = time.time() - orchestration_start_time

            # Log orchestration completion with detailed results
            log_llm_interaction(
                "ORCHESTRATION_COMPLETE",
                "OrchestrationRunner",
                response=str(result.get('final_response', 'No final response')) if result else 'No result',
                execution_time=orchestration_execution_time,
                extra_data={
                    "scenario_num": scenario_num,
                    "scenario_name": scenario['name'],
                    "success": result.get('success', False) if result else False,
                    "workflow_id": result.get('workflow_id', 'unknown') if result else 'unknown',
                    "query_analysis": result.get('query_analysis', {}) if result else {},
                    "agents_executed": {
                        "mysql": bool(result.get('mysql_analysis')) if result else False,
                        "shortage": bool(result.get('shortage_analysis')) if result else False,
                        "alert": bool(result.get('alert_management')) if result else False
                    }
                }
            )

            # Stream results if callback provided
            if stream_callback:
                success = result.get('success', False)
                execution_time = result.get('execution_time', 0)
                workflow_id = result.get('workflow_id', 'unknown')

                await stream_callback(f"Result: {'✓ SUCCESS' if success else '✗ FAILED'}\n")
                await stream_callback(f"Execution Time: {execution_time:.2f}s\n")
                await stream_callback(f"Workflow ID: {workflow_id}\n")

                if success:
                    query_analysis = result.get('query_analysis', {})
                    await stream_callback(f"Query Type: {query_analysis.get('type', 'unknown')}\n")
                    await stream_callback(f"Confidence: {query_analysis.get('confidence', 0):.2f}\n")
                    await stream_callback(f"Workflow Pattern: {query_analysis.get('workflow_pattern', 'unknown')}\n")

                    # Stream agent results
                    if result.get('mysql_analysis'):
                        mysql_analysis = result['mysql_analysis']
                        mysql_success = mysql_analysis.get('success', False)
                        await stream_callback(f"  MySQL Analysis: {'✓' if mysql_success else '✗'}\n")

                    if result.get('shortage_analysis'):
                        shortage_analysis = result['shortage_analysis']
                        shortage_success = shortage_analysis.get('success', False)
                        shortage_risk = shortage_analysis.get('risk_level', 'unknown')
                        await stream_callback(f"  Shortage Analysis: {'✓' if shortage_success else '✗'} (Risk: {shortage_risk})\n")

                    if result.get('alert_management'):
                        alert_management = result['alert_management']
                        alert_success = alert_management.get('success', False)
                        alerts_sent = len(alert_management.get('alerts_sent', []))
                        await stream_callback(f"  Alert Management: {'✓' if alert_success else '✗'} ({alerts_sent} alerts)\n")
                else:
                    error = result.get('error', 'Unknown error')
                    await stream_callback(f"Error: {error}\n")

                    # Check for clarification requirements
                    if result.get('requires_clarification', False):
                        await stream_callback("Query requires clarification:\n")
                        for clarification in result.get('suggested_clarifications', []):
                            await stream_callback(f"  - {clarification}\n")

            return result

        except Exception as e:
            self.logger.error(f"Demo scenario {scenario_num} failed with exception: {e}")
            error_result = {"success": False, "error": str(e), "scenario": scenario['name']}
            if stream_callback:
                await stream_callback(f"Demo scenario {scenario_num} failed: {e}\n")
            return error_result

    def get_orchestration_statistics(self) -> Dict[str, Any]:
        """Get orchestration execution statistics."""
        if not self.orchestration_runner:
            return {"error": "Orchestration runner not available"}
        return self.orchestration_runner.get_execution_statistics()

    def get_supported_query_types(self) -> List[str]:
        """Get list of supported query types."""
        if not self.orchestration_runner:
            return []
        return self.orchestration_runner.get_supported_query_types()

    def get_available_patterns(self) -> Dict[str, str]:
        """Get available workflow patterns."""
        if not self.orchestration_runner:
            return {}
        return self.orchestration_runner.get_available_patterns()

    async def get_or_create_session(
        self, 
        user_id: str, 
        session_type: SessionType, 
        company_name: str
    ) -> FinancialSession:
        """Get existing session or create a new one for the user."""
        session_key = f"{user_id}_{session_type.value}"
        
        if session_key in self.sessions:
            session = self.sessions[session_key]
            session.last_activity = datetime.now()
            return session

        # Create new session
        session_id = str(uuid.uuid4())
        session = FinancialSession(user_id, session_id, session_type)
        
        # Add reference to session manager for orchestration access
        session.session_manager = self

        try:
            await session.initialize(company_name)
            self.sessions[session_key] = session
            return session
        except Exception as e:
            await session.cleanup()
            raise Exception(f"Failed to create {session_type.value} session for user {user_id}: {str(e)}")

    async def cleanup_session(self, user_id: str, session_type: SessionType):
        """Clean up a specific user session."""
        session_key = f"{user_id}_{session_type.value}"
        if session_key in self.sessions:
            session = self.sessions[session_key]
            await session.cleanup()
            del self.sessions[session_key]

    async def cleanup(self):
        """Clean up all sessions."""
        cleanup_tasks = []
        for session_key, session in self.sessions.items():
            cleanup_tasks.append(session.cleanup())

        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        self.sessions.clear()

    async def _cleanup_task(self):
        """Background task to clean up inactive sessions."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)

                current_time = datetime.now()
                inactive_sessions = []

                for session_key, session in self.sessions.items():
                    time_since_activity = (
                        current_time - session.last_activity
                    ).total_seconds()
                    if time_since_activity > self.max_inactive_time:
                        inactive_sessions.append(session_key)

                # Clean up inactive sessions
                for session_key in inactive_sessions:
                    print(f"Cleaning up inactive session: {session_key}")
                    session = self.sessions[session_key]
                    await session.cleanup()
                    del self.sessions[session_key]

            except Exception as e:
                print(f"Error in cleanup task: {e}")

    def get_session_info(self, user_id: str, session_type: SessionType) -> Optional[dict]:
        """Get session information for a user and session type."""
        session_key = f"{user_id}_{session_type.value}"
        if session_key not in self.sessions:
            return None

        session = self.sessions[session_key]
        return {
            "user_id": session.user_id,
            "session_id": session.session_id,
            "session_type": session.session_type.value,
            "company_name": session.company_name,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": len(session.message_history),
        }
