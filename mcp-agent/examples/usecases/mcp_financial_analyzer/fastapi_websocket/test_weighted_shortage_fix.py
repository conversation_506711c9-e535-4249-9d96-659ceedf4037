#!/usr/bin/env python3
"""
Test script to verify the WeightedShortageIndex fix is working correctly.
This test directly calls the WeightedShortageIndex tool with problematic weights.
"""

import asyncio
import json
import sys
import os

# Add the parent directory to the path to import the agents
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.shortage_analyzer_agent import create_shortage_analyzer_agent, MCPToolIntegration


async def test_weighted_shortage_fix():
    """Test the WeightedShortageIndex tool fix with problematic weights."""
    print("=== Testing WeightedShortageIndex Fix ===")
    
    # Initialize the shortage analyzer agent
    agent = create_shortage_analyzer_agent("test_company")
    mcp_integration = MCPToolIntegration(agent)
    
    # Test cases that previously failed
    test_cases = [
        {
            "name": "Floating-point precision issue 1",
            "weights": [0.1, 0.2, 0.3, 0.4000000000000001],  # Sum: 1.0000000000000002
            "required_qty": [1000, 2000, 3000, 4000],
            "available_qty": [500, 1000, 1500, 2000]
        },
        {
            "name": "Floating-point precision issue 2", 
            "weights": [0.2, 0.3, 0.499999999999999],  # Sum: 0.999999999999999
            "required_qty": [1000, 2000, 3000],
            "available_qty": [500, 1000, 1500]
        },
        {
            "name": "Complex floating-point case",
            "weights": [1/3, 1/3, 1/3],  # These should sum to 1.0 but might have precision issues
            "required_qty": [3000, 6000, 9000],
            "available_qty": [1000, 2000, 3000]
        },
        {
            "name": "Edge case with very small differences",
            "weights": [0.25, 0.25, 0.25, 0.25 + 1e-15],  # Sum very close to 1.0
            "required_qty": [1000, 2000, 3000, 4000],
            "available_qty": [100, 200, 300, 400]
        }
    ]
    
    print(f"\nTesting {len(test_cases)} problematic weight scenarios...\n")
    
    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- Test Case {i}: {test_case['name']} ---")
        weights = test_case['weights']
        weight_sum = sum(weights)
        
        print(f"Weights: {weights}")
        print(f"Sum: {weight_sum}")
        print(f"Sum == 1.0: {weight_sum == 1.0}")
        
        try:
            result = await mcp_integration.call_weighted_shortage_tool(
                required_qty=test_case['required_qty'],
                available_qty=test_case['available_qty'],
                weights=weights
            )
            
            if result.get('error') is None:
                print(f"✅ SUCCESS: Weighted shortage index = {result.get('weighted_shortage_index')}")
                success_count += 1
            else:
                print(f"❌ FAILED: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ EXCEPTION: {e}")
        
        print()
    
    print(f"=== Test Results ===")
    print(f"Successful tests: {success_count}/{len(test_cases)}")
    print(f"Fix effectiveness: {success_count/len(test_cases)*100:.1f}%")
    
    if success_count == len(test_cases):
        print("🎉 All tests passed! The WeightedShortageIndex fix is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. The fix may need additional work.")
        return False


async def test_before_and_after_comparison():
    """Show what would have happened before and after the fix."""
    print("\n=== Before/After Comparison ===")
    
    # Test weights that would fail with exact equality but pass with tolerance
    problematic_weights = [0.1, 0.2, 0.3, 0.4000000000000001]  # Sum: 1.0000000000000002
    weight_sum = sum(problematic_weights)
    
    print(f"Test weights: {problematic_weights}")
    print(f"Sum: {weight_sum}")
    print(f"Sum == 1.0 (old validation): {weight_sum == 1.0}")
    
    # Simulate the old validation
    import math
    print(f"math.isclose(sum, 1.0, rel_tol=1e-6) (new validation): {math.isclose(weight_sum, 1.0, rel_tol=1e-6)}")
    
    print("\nBefore fix: Would fail with 'Weights must sum to 1' error")
    print("After fix: Should pass with floating-point tolerance")
    
    # Test with the actual tool
    agent = create_shortage_analyzer_agent("test_company")
    mcp_integration = MCPToolIntegration(agent)
    
    try:
        result = await mcp_integration.call_weighted_shortage_tool(
            required_qty=[1000, 2000, 3000, 4000],
            available_qty=[500, 1000, 1500, 2000],
            weights=problematic_weights
        )
        
        if result.get('error') is None:
            print(f"✅ Actual result: SUCCESS - Weighted shortage index = {result.get('weighted_shortage_index')}")
        else:
            print(f"❌ Actual result: FAILED - {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Actual result: EXCEPTION - {e}")


async def main():
    """Main test function."""
    print("WeightedShortageIndex Fix Verification Test")
    print("=" * 50)
    
    # Run the main fix test
    fix_works = await test_weighted_shortage_fix()
    
    # Show before/after comparison
    await test_before_and_after_comparison()
    
    print("\n" + "=" * 50)
    if fix_works:
        print("🎉 CONCLUSION: The WeightedShortageIndex fix is working correctly!")
        print("   The tool now uses floating-point tolerance instead of exact equality.")
        print("   This resolves the 'Weights must sum to 1' error for precision issues.")
    else:
        print("⚠️  CONCLUSION: The fix needs additional work.")
    
    return fix_works


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
