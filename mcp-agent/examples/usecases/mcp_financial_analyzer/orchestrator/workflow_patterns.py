"""
Workflow Patterns for Financial Orchestrator
==========================================

Predefined workflow templates and execution patterns for common financial
analysis scenarios. Each pattern defines agent sequences, data flow, and
execution strategies optimized for specific use cases.

Supported Patterns:
- Shortage Analysis: MySQL → Shortage → Alert (inventory risk assessment)
- Supplier Risk: MySQL → Shortage (vendor reliability analysis)
- Customer Priority: MySQL → Alert (delivery and priority management)
- Comprehensive: MySQL → Shortage → Alert (full analysis pipeline)
"""

import asyncio
import inspect
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod

from mcp_agent.logging.logger import get_logger
from .exceptions import OrchestrationError, AgentExecutionError

logger = get_logger(__name__)


class ExecutionMode(Enum):
    """Workflow execution mode options."""
    SEQUENTIAL = "sequential"  # Execute agents one after another
    PARALLEL = "parallel"  # Execute independent agents concurrently
    CONDITIONAL = "conditional"  # Execute based on previous results
    HYBRID = "hybrid"  # Mix of sequential and parallel execution


@dataclass
class WorkflowStep:
    """Individual step in a workflow pattern."""
    step_id: str
    agent_name: str
    description: str
    name: str = ""  # Human/machine-readable name; defaults to step_id
    required_inputs: List[str] = field(default_factory=list)
    expected_outputs: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)  # Other step IDs this depends on
    parallel_with: List[str] = field(default_factory=list)  # Steps that can run in parallel
    timeout: int = 120  # Timeout in seconds
    retry_count: int = 2
    optional: bool = False  # Can workflow continue if this step fails?

    def __post_init__(self) -> None:
        # Ensure name is populated for tests that expect step.name
        if not self.name:
            self.name = self.step_id

    @property
    def required(self) -> bool:
        """Whether this step is required (the inverse of optional)."""
        return not self.optional

    def can_execute(self, completed_steps: List[str]) -> bool:
        """Check if this step can execute based on dependencies."""
        return all(dep in completed_steps for dep in self.dependencies)


@dataclass
class WorkflowPattern:
    """Complete workflow pattern definition."""
    pattern_id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    estimated_duration: int = 300  # Seconds
    success_criteria: List[str] = field(default_factory=list)
    failure_recovery: str = "graceful_degradation"
    context_sharing: bool = True
    
    def get_execution_order(self) -> List[List[str]]:
        """Get the execution order as list of step groups (parallel groups)."""
        if self.execution_mode == ExecutionMode.SEQUENTIAL:
            return [[step.step_id] for step in self.steps]
        
        # For parallel/hybrid modes, group steps that can run together
        execution_groups = []
        remaining_steps = [step.step_id for step in self.steps]
        completed_steps = []
        
        while remaining_steps:
            # Find all steps that can execute now
            ready_steps = []
            for step_id in remaining_steps:
                step = self.get_step(step_id)
                if step and step.can_execute(completed_steps):
                    ready_steps.append(step_id)
            
            if not ready_steps:
                # Deadlock - add remaining steps sequentially
                execution_groups.extend([[step_id] for step_id in remaining_steps])
                break
            
            # Group parallel-executable steps
            if self.execution_mode in [ExecutionMode.PARALLEL, ExecutionMode.HYBRID]:
                parallel_group = []
                for step_id in ready_steps:
                    step = self.get_step(step_id)
                    if step and (not step.dependencies or 
                               any(other_step in parallel_group 
                                   for other_step in step.parallel_with)):
                        parallel_group.append(step_id)
                
                if parallel_group:
                    execution_groups.append(parallel_group)
                else:
                    execution_groups.append([ready_steps[0]])  # Sequential fallback
            else:
                execution_groups.append([ready_steps[0]])
            
            # Update tracking
            for step_id in execution_groups[-1]:
                remaining_steps.remove(step_id)
                completed_steps.append(step_id)
        
        return execution_groups
    
    def get_step(self, step_id: str) -> Optional[WorkflowStep]:
        """Get a workflow step by ID."""
        for step in self.steps:
            if step.step_id == step_id:
                return step
        return None


class WorkflowPatternRegistry:
    """Registry of predefined workflow patterns for financial analysis."""
    
    def __init__(self):
        """Initialize the pattern registry with standard patterns."""
        self.patterns: Dict[str, WorkflowPattern] = {}
        self._register_standard_patterns()
        logger.info(f"WorkflowPatternRegistry initialized with {len(self.patterns)} patterns")
    
    def _register_standard_patterns(self):
        """Register all standard workflow patterns."""
        
        # 1. Shortage Analysis Pattern (MySQL → Shortage → Alert)
        shortage_pattern = WorkflowPattern(
            pattern_id="shortage_analysis",
            name="Shortage Analysis Workflow", 
            description="Comprehensive inventory shortage analysis with alerts",
            steps=[
                WorkflowStep(
                    step_id="mysql_data_analysis",
                    agent_name="mysql_analyzer",
                    description="Analyze historical data and current inventory levels",
                    required_inputs=["financial_query", "entity_context"],
                    expected_outputs=["mysql_results", "inventory_data", "supplier_data"],
                    timeout=120,
                    retry_count=2
                ),
                WorkflowStep(
                    step_id="shortage_calculation",
                    agent_name="shortage_analyzer", 
                    description="Calculate shortage indices and risk levels",
                    required_inputs=["mysql_results", "component_data"],
                    expected_outputs=["shortage_index", "risk_level", "recommendations"],
                    dependencies=["mysql_data_analysis"],
                    timeout=90,
                    retry_count=2
                ),
                WorkflowStep(
                    step_id="alert_processing",
                    agent_name="alert_manager",
                    description="Process shortage results and send notifications",
                    required_inputs=["shortage_results", "alert_criteria"],
                    expected_outputs=["alerts_sent", "notification_status"],
                    dependencies=["shortage_calculation"],
                    timeout=60,
                    retry_count=1,
                    optional=True  # Workflow can succeed without alerts
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            estimated_duration=270,
            success_criteria=["mysql_success", "shortage_calculated"],
            context_sharing=True
        )
        self.patterns["shortage_analysis"] = shortage_pattern
        
        # 2. Supplier Risk Assessment Pattern (MySQL → Shortage)
        supplier_risk_pattern = WorkflowPattern(
            pattern_id="supplier_risk",
            name="Supplier Risk Assessment",
            description="Analyze supplier reliability and delivery risks",
            steps=[
                WorkflowStep(
                    step_id="supplier_data_analysis",
                    agent_name="mysql_analyzer",
                    description="Analyze supplier performance and delivery history",
                    required_inputs=["supplier_query", "time_range"],
                    expected_outputs=["supplier_metrics", "delivery_performance", "risk_factors"],
                    timeout=150
                ),
                WorkflowStep(
                    step_id="risk_weighted_shortage",
                    agent_name="shortage_analyzer",
                    description="Calculate risk-weighted shortage analysis",
                    required_inputs=["supplier_metrics", "inventory_data"],
                    expected_outputs=["risk_weighted_index", "supplier_risk_level"],
                    dependencies=["supplier_data_analysis"],
                    timeout=90
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            estimated_duration=240,
            success_criteria=["supplier_analysis_complete", "risk_assessment_complete"]
        )
        self.patterns["supplier_risk"] = supplier_risk_pattern
        
        # 3. Customer Priority Management Pattern (MySQL → Alert)
        customer_priority_pattern = WorkflowPattern(
            pattern_id="customer_priority",
            name="Customer Priority Management",
            description="Analyze customer orders and manage delivery priorities",
            steps=[
                WorkflowStep(
                    step_id="customer_order_analysis",
                    agent_name="mysql_analyzer",
                    description="Analyze customer orders and delivery commitments",
                    required_inputs=["customer_query", "order_data"],
                    expected_outputs=["customer_priorities", "delivery_schedule", "conflicts"],
                    timeout=120
                ),
                WorkflowStep(
                    step_id="priority_alert_processing",
                    agent_name="alert_manager",
                    description="Process priority conflicts and send customer notifications",
                    required_inputs=["customer_priorities", "delivery_conflicts"],
                    expected_outputs=["priority_alerts", "customer_notifications"],
                    dependencies=["customer_order_analysis"],
                    timeout=90
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            estimated_duration=210,
            success_criteria=["customer_analysis_complete", "alerts_processed"]
        )
        self.patterns["customer_priority"] = customer_priority_pattern
        
        # 4. Comprehensive Analysis Pattern (MySQL → Shortage → Alert)
        comprehensive_pattern = WorkflowPattern(
            pattern_id="comprehensive",
            name="Comprehensive Financial Analysis",
            description="Full-spectrum financial and operational analysis",
            steps=[
                WorkflowStep(
                    step_id="comprehensive_data_analysis",
                    agent_name="mysql_analyzer",
                    description="Comprehensive data analysis across all dimensions",
                    required_inputs=["complex_query", "full_context"],
                    expected_outputs=["comprehensive_data", "multi_dimensional_analysis"],
                    timeout=180,
                    retry_count=3
                ),
                WorkflowStep(
                    step_id="comprehensive_shortage_analysis",
                    agent_name="shortage_analyzer",
                    description="Multi-component shortage and risk analysis",
                    required_inputs=["comprehensive_data", "all_components"],
                    expected_outputs=["multi_shortage_analysis", "comprehensive_risk_assessment"],
                    dependencies=["comprehensive_data_analysis"],
                    timeout=120,
                    retry_count=2
                ),
                WorkflowStep(
                    step_id="comprehensive_alert_processing",
                    agent_name="alert_manager",
                    description="Process comprehensive results and send all relevant alerts",
                    required_inputs=["multi_shortage_analysis", "comprehensive_context"],
                    expected_outputs=["comprehensive_alerts", "full_notifications"],
                    dependencies=["comprehensive_shortage_analysis"],
                    timeout=90,
                    retry_count=1,
                    optional=True
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            estimated_duration=390,
            success_criteria=["comprehensive_analysis_complete", "multi_dimensional_results"],
            context_sharing=True
        )
        self.patterns["comprehensive"] = comprehensive_pattern
        
        # 5. Parallel Analysis Pattern (experimental)
        parallel_pattern = WorkflowPattern(
            pattern_id="parallel_analysis",
            name="Parallel Financial Analysis",
            description="Concurrent analysis for independent data streams",
            steps=[
                WorkflowStep(
                    step_id="mysql_historical_analysis",
                    agent_name="mysql_analyzer",
                    description="Historical data analysis",
                    required_inputs=["historical_query"],
                    expected_outputs=["historical_results"],
                    timeout=120,
                    parallel_with=["shortage_current_analysis"]
                ),
                WorkflowStep(
                    step_id="shortage_current_analysis", 
                    agent_name="shortage_analyzer",
                    description="Current shortage analysis",
                    required_inputs=["current_inventory_data"],
                    expected_outputs=["current_shortage_results"],
                    timeout=90,
                    parallel_with=["mysql_historical_analysis"]
                ),
                WorkflowStep(
                    step_id="consolidated_alert_processing",
                    agent_name="alert_manager",
                    description="Consolidate parallel results and send alerts",
                    required_inputs=["historical_results", "current_shortage_results"],
                    expected_outputs=["consolidated_alerts"],
                    dependencies=["mysql_historical_analysis", "shortage_current_analysis"],
                    timeout=60
                )
            ],
            execution_mode=ExecutionMode.HYBRID,
            estimated_duration=180,
            success_criteria=["parallel_analysis_complete", "results_consolidated"]
        )
        self.patterns["parallel_analysis"] = parallel_pattern

    def get_pattern(self, pattern_id: str) -> Optional[WorkflowPattern]:
        """Get a workflow pattern by ID."""
        return self.patterns.get(pattern_id)

    def get_all_patterns(self) -> Dict[str, WorkflowPattern]:
        """Get all registered workflow patterns."""
        return self.patterns.copy()

    def get_pattern_by_name(self, name: str) -> Optional[WorkflowPattern]:
        """Get a workflow pattern by its name field (not the ID)."""
        for p in self.patterns.values():
            if p.name == name:
                return p
        # Also allow matching by ID when tests pass names equal to IDs
        return self.patterns.get(name)

    def register_pattern(self, pattern: WorkflowPattern) -> None:
        """Register a new workflow pattern."""
        self.patterns[pattern.pattern_id] = pattern
        logger.info(f"Registered new workflow pattern: {pattern.pattern_id}")

    def get_pattern_for_query_type(self, query_type: str) -> Optional[WorkflowPattern]:
        """Get the most appropriate pattern for a query type."""
        pattern_mapping = {
            "shortage_analysis": "shortage_analysis",
            "supplier_risk": "supplier_risk",
            "customer_priority": "customer_priority",
            "comprehensive": "comprehensive",
            "inventory_check": "shortage_analysis",
            "order_status": "customer_priority",
            "production_planning": "comprehensive",
            "cost_analysis": "comprehensive",
            "unknown": "comprehensive"
        }

        pattern_id = pattern_mapping.get(query_type, "comprehensive")
        return self.get_pattern(pattern_id)


class WorkflowExecutor:
    """Executor for workflow patterns with advanced orchestration capabilities."""
    
    def __init__(self, pattern_registry: WorkflowPatternRegistry):
        """Initialize workflow executor."""
        self.registry = pattern_registry
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        logger.info("WorkflowExecutor initialized")
    
    async def execute_pattern(
        self,
        pattern_id: str,
        workflow_id: str,
        agents: Dict[str, Any],
        context_manager: Any,
        input_data: Dict[str, Any],
        stream_callback: Optional[Callable[[str], None]] = None,
        tool_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        early_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
    ) -> Dict[str, Any]:
        """Execute a workflow pattern with the provided agents."""
        pattern = self.registry.get_pattern(pattern_id)
        if not pattern:
            raise OrchestrationError(
                message=f"Unknown workflow pattern: {pattern_id}",
                workflow_id=workflow_id,
                error_code="UNKNOWN_WORKFLOW_PATTERN",
            )

        logger.info(f"Executing workflow pattern {pattern_id} for workflow {workflow_id}")

        # Enhanced logging for tool streaming diagnostics
        if tool_stream_callback:
            logger.info(f"Workflow executor: Tool streaming callback available for pattern {pattern_id}")
        else:
            logger.warning(f"Workflow executor: No tool streaming callback for pattern {pattern_id}")

        # Initialize workflow state
        workflow_state = {
            "workflow_id": workflow_id,
            "pattern_id": pattern_id,
            "status": "running",
            "completed_steps": [],
            "failed_steps": [],
            "step_results": {},
            "start_time": asyncio.get_event_loop().time(),
            "context": input_data,
            "stream_callback": stream_callback,
            "tool_stream_callback": tool_stream_callback,  # Store callback in state
            "early_stream_callback": early_stream_callback,
        }
        
        self.active_workflows[workflow_id] = workflow_state
        
        try:
            # Get execution order
            execution_groups = pattern.get_execution_order()
            
            # Execute workflow groups
            for group_index, step_group in enumerate(execution_groups):
                logger.info(f"Executing step group {group_index + 1}: {step_group}")
                
                if len(step_group) == 1:
                    # Sequential execution
                    step_id = step_group[0]

                    # Extra diagnostics: surface early VLLM events before shortage step
                    # Enabled when EARLY_VLLM_DEBUG=1 is present in the environment.
                    if os.getenv("EARLY_VLLM_DEBUG", "0") == "1" and "shortage" in step_id.lower():
                        try:
                            debug_payload_base = {
                                "source": "shortage_analyzer",
                                "timestamp": datetime.utcnow().isoformat() + "Z",
                            }
                            # Emit an explicit early-phase start
                            await self._emit_early_stream_event(
                                early_stream_callback,
                                {
                                    **debug_payload_base,
                                    "type": "vllm_response_start",
                                    "phase": "reasoning",
                                    "message": "[debug] Orchestration pre-shortage reasoning start",
                                },
                                workflow_state,
                            )

                            # Provide a small reasoning chunk from current context
                            ctx = workflow_state.get("context", {}) if isinstance(workflow_state, dict) else {}
                            original_query = ctx.get("original_query") if isinstance(ctx, dict) else None
                            if isinstance(original_query, str) and original_query:
                                snippet = original_query[:220]
                                await self._emit_early_stream_event(
                                    early_stream_callback,
                                    {
                                        **debug_payload_base,
                                        "type": "vllm_response_chunk",
                                        "phase": "reasoning",
                                        "message": {
                                            "content": f"[debug] Context snippet: {snippet}",
                                            "chunk_index": 1,
                                            "chunk_chars": len(snippet),
                                            "cumulative_chars": len(snippet),
                                        },
                                    },
                                    workflow_state,
                                )

                            # Mark early phase complete from executor perspective
                            await self._emit_early_stream_event(
                                early_stream_callback,
                                {
                                    **debug_payload_base,
                                    "type": "vllm_response_complete",
                                    "phase": "reasoning_complete",
                                    "message": {"tool_count": 1, "total_chunks": 1, "total_chars": 0},
                                },
                                workflow_state,
                            )
                        except Exception as diag_err:
                            logger.debug(f"EARLY_VLLM_DEBUG emit failed: {diag_err}")
                    result = await self._execute_step(
                        step_id,
                        pattern,
                        agents,
                        context_manager,
                        workflow_state,
                        tool_stream_callback,
                        early_stream_callback,
                    )
                    workflow_state["step_results"][step_id] = result
                else:
                    # Parallel execution
                    tasks = []
                    for step_id in step_group:
                        task = self._execute_step(
                            step_id,
                            pattern,
                            agents,
                            context_manager,
                            workflow_state,
                            tool_stream_callback,
                            early_stream_callback,
                        )
                        tasks.append((step_id, task))
                    
                    # Wait for all parallel tasks
                    for step_id, task in tasks:
                        try:
                            result = await task
                            workflow_state["step_results"][step_id] = result
                        except Exception as e:
                            logger.error(f"Parallel step {step_id} failed: {e}")
                            workflow_state["failed_steps"].append(step_id)
                            workflow_state["step_results"][step_id] = {"error": str(e)}
                
                # Check if workflow can continue
                if not self._can_continue_workflow(pattern, workflow_state):
                    logger.warning(f"Workflow {workflow_id} cannot continue after group {group_index + 1}")
                    break
            
            # Finalize workflow
            workflow_state["status"] = "completed"
            workflow_state["end_time"] = asyncio.get_event_loop().time()
            workflow_state["duration"] = workflow_state["end_time"] - workflow_state["start_time"]
            
            logger.info(f"Workflow {workflow_id} completed in {workflow_state['duration']:.2f}s")
            
            return self._compile_workflow_results(pattern, workflow_state)
            
        except OrchestrationError as e:
            # Propagate orchestrator-specific errors per API spec
            logger.error(f"Workflow {workflow_id} failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Workflow {workflow_id} failed: {e}")
            workflow_state["status"] = "failed"
            workflow_state["error"] = str(e)
            return {"success": False, "error": str(e), "workflow_state": workflow_state}

        finally:
            # Cleanup
            if workflow_id in self.active_workflows:
                del self.active_workflows[workflow_id]
    
    async def _execute_step(
        self,
        step_id: str,
        pattern: WorkflowPattern,
        agents: Dict[str, Any],
        context_manager: Any,
        workflow_state: Dict[str, Any],
        tool_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        early_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
    ) -> Dict[str, Any]:
        """Execute a single workflow step."""
        step = pattern.get_step(step_id)
        if not step:
            raise OrchestrationError(
                message=f"Step {step_id} not found in pattern",
                workflow_id=workflow_state.get("workflow_id"),
                error_code="STEP_NOT_FOUND",
            )

        agent = agents.get(step.agent_name)
        if not agent:
            raise AgentExecutionError(
                message=f"Agent {step.agent_name} not available",
                agent_name=step.agent_name,
                step_id=step_id,
                workflow_id=workflow_state.get("workflow_id"),
                error_code="AGENT_NOT_AVAILABLE",
            )
        
        logger.info(f"Executing step {step_id}: {step.description}")

        # Get callbacks (prefer explicit overrides, fallback to workflow state)
        stream_callback = workflow_state.get("stream_callback")
        if tool_stream_callback is None:
            tool_stream_callback = workflow_state.get("tool_stream_callback")
        if early_stream_callback is None:
            early_stream_callback = workflow_state.get("early_stream_callback")

        if tool_stream_callback:
            logger.info(f"Step {step_id}: Tool streaming callback available")
        else:
            logger.warning(f"Step {step_id}: No tool streaming callback available")

        workflow_state["current_step"] = step.agent_name

        await self._emit_stream_message(
            stream_callback,
            f"Starting {step.name} stage via {step.agent_name.replace('_', ' ')} agent...\n",
            workflow_state,
            label="workflow",
            source=step.agent_name,
        )

        # Prepare step context
        step_context = self._prepare_step_context(step, workflow_state, context_manager)

        try:
            # Execute step with timeout
            start_time = asyncio.get_event_loop().time()
            
            if step.agent_name == "mysql_analyzer":
                result = await self._execute_mysql_step(
                    agent,
                    step_context,
                    step.timeout,
                    stream_callback,
                    tool_stream_callback,
                    workflow_state,
                    early_stream_callback,
                )
            elif step.agent_name == "shortage_analyzer":
                result = await self._execute_shortage_step(
                    agent,
                    step_context,
                    step.timeout,
                    stream_callback,
                    tool_stream_callback,
                    workflow_state,
                    early_stream_callback,
                )
            elif step.agent_name == "alert_manager":
                result = await self._execute_alert_step(
                    agent,
                    step_context,
                    step.timeout,
                    stream_callback,
                    tool_stream_callback,
                    workflow_state,
                    early_stream_callback,
                )
            else:
                raise ValueError(f"Unknown agent type: {step.agent_name}")

            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            # Update context manager
            if context_manager:
                if step.agent_name == "mysql_analyzer":
                    context_manager.update_mysql_context(
                        workflow_state["workflow_id"], result, execution_time
                    )
                elif step.agent_name == "shortage_analyzer":
                    context_manager.update_shortage_context(
                        workflow_state["workflow_id"], result, execution_time
                    )
                elif step.agent_name == "alert_manager":
                    context_manager.update_alert_context(
                        workflow_state["workflow_id"], result, execution_time
                    )
            
            workflow_state["completed_steps"].append(step_id)
            logger.info(f"Step {step_id} completed successfully in {execution_time:.2f}s")

            await self._emit_stream_message(
                stream_callback,
                f"{step.name} stage completed in {execution_time:.2f}s\n",
                workflow_state,
                label="workflow",
                source=step.agent_name,
            )

            return {"success": True, "result": result, "execution_time": execution_time}

        except Exception as e:
            logger.error(f"Step {step_id} failed: {e}")
            if not step.optional:
                workflow_state["failed_steps"].append(step_id)
            await self._emit_stream_message(
                stream_callback,
                f"{step.name} stage failed: {e}\n",
                workflow_state,
                label="workflow",
                source=step.agent_name,
            )
            return {"success": False, "error": str(e)}
        finally:
            workflow_state.pop("current_step", None)

    async def _emit_stream_message(
        self,
        stream_callback: Optional[Callable[[Any], Any]],
        message: str,
        workflow_state: Optional[Dict[str, Any]] = None,
        *,
        label: str = "workflow",
        source: Optional[str] = None,
    ) -> None:
        if not stream_callback or not message:
            return

        # Skip workflow messages - removed per user request
        if label == "workflow":
            return

        try:
            safe_text = message if isinstance(message, str) else str(message)

            payload: Dict[str, Any] = {
                "type": label.lower().replace(" ", "_"),
                "label": label,
                "content": safe_text,
                "timestamp": datetime.utcnow().isoformat() + "Z",
            }

            if workflow_state is not None:
                stream_stats = workflow_state.setdefault(
                    "_stream_stats", {"chunk_index": 0, "total_chars": 0}
                )
            else:
                stream_stats = {"chunk_index": 0, "total_chars": 0}

            if label.lower() == "llm response":
                stream_stats["chunk_index"] += 1
                chunk_length = len(safe_text)
                stream_stats["total_chars"] += chunk_length
                payload["chunk_index"] = stream_stats["chunk_index"]
                payload["metrics"] = {
                    "chunk_chars": chunk_length,
                    "cumulative_chars": stream_stats["total_chars"],
                }

            if workflow_state is not None:
                payload["workflow_id"] = workflow_state.get("workflow_id")

            payload["source"] = (
                source
                or (workflow_state.get("current_step") if workflow_state else None)
                or "workflow_executor"
            )

            result = stream_callback(payload)
            if inspect.isawaitable(result):
                await result
        except Exception as stream_error:
            logger.debug(f"Failed to emit stream message: {stream_error}")

    async def _emit_early_stream_event(
        self,
        early_stream_callback: Optional[Callable[[Dict[str, Any]], None]],
        payload: Optional[Dict[str, Any]],
        workflow_state: Optional[Dict[str, Any]] = None,
    ) -> None:
        if not payload:
            logger.info("Early stream event skipped: empty payload")
            return

        callback: Optional[Callable[[Dict[str, Any]], None]] = early_stream_callback
        if callback is None and workflow_state is not None:
            callback = workflow_state.get("early_stream_callback")
        if callback is None and workflow_state is not None:
            callback = workflow_state.get("stream_callback")
        if callback is None:
            logger.info(
                "Early stream event dropped: no callback available | event_type=%s",
                payload.get("type"),
            )
            return

        try:
            # Avoid passing positional args to the structured logger to prevent
            # Pydantic validation errors (it may interpret the 2nd/3rd args as
            # data/context). Format the message eagerly and pass as a single string.
            has_wf_cb = False
            if workflow_state is not None:
                try:
                    has_wf_cb = bool(callback is workflow_state.get("stream_callback"))
                except Exception:
                    has_wf_cb = False
            logger.info(
                f"Emitting early stream event | type={payload.get('type')} | has_workflow_callback={has_wf_cb}"
            )
            result = callback(payload)
            if inspect.isawaitable(result):
                await result
        except Exception as stream_error:
            logger.info(f"Failed to emit early stream event: {stream_error}")

    @staticmethod
    def _format_json_snippet(payload: Any, limit: int = 400) -> str:
        if payload is None:
            return "null"
        try:
            text = json.dumps(payload, ensure_ascii=False, default=str)
        except Exception:
            text = str(payload)
        text = text.strip()
        if len(text) > limit:
            return f"{text[:limit]}... (truncated)"
        return text if text else "\"\""

    @staticmethod
    def _ensure_json_serializable(value: Any) -> Any:
        if isinstance(value, (dict, list, str, int, float, bool)) or value is None:
            return value
        if hasattr(value, "model_dump"):
            try:
                return value.model_dump()
            except Exception:
                pass
        if hasattr(value, "dict"):
            try:
                return value.dict()  # type: ignore[attr-defined]
            except Exception:
                pass
        if hasattr(value, "__iter__") and not isinstance(value, (str, bytes)):
            try:
                return list(value)  # type: ignore[arg-type]
            except Exception:
                pass
        return str(value)

    def _prepare_step_context(
        self,
        step: WorkflowStep,
        workflow_state: Dict[str, Any],
        context_manager: Any
    ) -> Dict[str, Any]:
        """Prepare context data for step execution."""
        context = {
            "original_query": workflow_state["context"].get("original_query", ""),
            "workflow_id": workflow_state["workflow_id"],
            "step_id": step.step_id,
        }
        
        # Add previous step results
        for completed_step in workflow_state["completed_steps"]:
            if completed_step in workflow_state["step_results"]:
                step_result = workflow_state["step_results"][completed_step]
                context[f"{completed_step}_result"] = step_result
        
        # Add context manager data if available
        if context_manager:
            shared_context = context_manager.get_context_for_agent(
                workflow_state["workflow_id"], step.agent_name
            )
            context["shared_context"] = shared_context
        
        return context
    
    async def _execute_mysql_step(
        self,
        agent: Any,
        context: Dict[str, Any],
        timeout: int,
        stream_callback: Optional[Callable[[str], Any]] = None,
        tool_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        workflow_state: Optional[Dict[str, Any]] = None,
        early_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
    ) -> Dict[str, Any]:
        """Execute MySQL agent step."""
        try:
            from agents.mysql_agent import MCPOrchestratorInputSchema, safe_orchestrator_run, tool_schema_to_class_map, FinalResponseSchema

            query = context.get("original_query", "")
            mysql_input = MCPOrchestratorInputSchema(query=query)

            await self._emit_stream_message(
                stream_callback,
                "MySQL analyzer assessing database context...\n",
                workflow_state,
                label="workflow",
                source="mysql_analyzer",
            )

            # Send tool stream event for MySQL step start
            if tool_stream_callback:
                await tool_stream_callback({
                    "type": "tool_call_start",
                    "tool_name": "mysql_analyzer",
                    "args": {"query": query},
                    "timestamp": asyncio.get_event_loop().time()
                })
                logger.info("MySQL step: Sent tool_call_start event")

            mysql_output = await asyncio.to_thread(
                safe_orchestrator_run,
                agent,
                mysql_input,
            )
            action_instance = mysql_output.action
            reasoning = mysql_output.reasoning
            tool_outputs: List[Dict[str, Any]] = []
            tool_queries: List[str] = []

            if reasoning:
                timestamp = datetime.utcnow().isoformat() + "Z"
                event_base = {
                    "source": "mysql_analyzer",
                    "timestamp": timestamp,
                }
                if workflow_state and workflow_state.get("workflow_id"):
                    event_base["workflow_id"] = workflow_state["workflow_id"]

                await self._emit_early_stream_event(
                    early_stream_callback,
                    {
                        **event_base,
                        "type": "vllm_response_start",
                        "phase": "reasoning",
                        "message": "MySQL analyzer reasoning phase started",
                    },
                    workflow_state,
                )

                trimmed_reasoning = reasoning.strip()
                if trimmed_reasoning:
                    chunk_chars = len(trimmed_reasoning)
                    await self._emit_early_stream_event(
                        early_stream_callback,
                        {
                            **event_base,
                            "type": "vllm_response_chunk",
                            "phase": "reasoning",
                            "message": {
                                "content": trimmed_reasoning,
                                "chunk_index": 1,
                                "chunk_chars": chunk_chars,
                                "cumulative_chars": chunk_chars,
                            },
                        },
                        workflow_state,
                    )

                    await self._emit_early_stream_event(
                        early_stream_callback,
                        {
                            **event_base,
                            "type": "vllm_response_complete",
                            "phase": "reasoning_complete",
                            "message": {
                                "tool_count": len(tool_schema_to_class_map) if tool_schema_to_class_map else 0,
                                "total_chunks": 1,
                                "total_chars": chunk_chars,
                            },
                        },
                        workflow_state,
                    )

                await self._emit_stream_message(
                    stream_callback,
                    f"MySQL reasoning:\n{reasoning}\n\n",
                    workflow_state,
                    label="LLM response",
                    source="mysql_analyzer",
                )

            # Continue execution until final response
            max_iterations = 10
            iteration = 0

            while not isinstance(action_instance, FinalResponseSchema) and iteration < max_iterations:
                schema_type = type(action_instance)
                ToolClass = tool_schema_to_class_map.get(schema_type)

                if not ToolClass:
                    break

                tool_name = getattr(ToolClass, 'mcp_tool_name', 'unknown_mysql_tool')

                tool_args: Dict[str, Any] = {}
                if hasattr(action_instance, "model_dump"):
                    try:
                        tool_args = action_instance.model_dump()
                    except Exception:
                        tool_args = {}
                elif hasattr(action_instance, "dict"):
                    try:
                        tool_args = action_instance.dict()  # type: ignore[attr-defined]
                    except Exception:
                        tool_args = {}

                call_started = asyncio.get_event_loop().time()

                if tool_stream_callback:
                    await tool_stream_callback({
                        "type": "tool_call_start",
                        "tool_name": tool_name,
                        "args": tool_args,
                        "timestamp": call_started,
                        "iteration": iteration + 1,
                    })
                    logger.info(f"MySQL step: Sent tool_call_start for {tool_name}")

                await self._emit_stream_message(
                    stream_callback,
                    f"🔧 Executing {tool_name} with args: {self._format_json_snippet(tool_args)}\n",
                    workflow_state,
                    label="workflow",
                    source="mysql_analyzer",
                )

                tool_instance = ToolClass()
                tool_output = await asyncio.to_thread(
                    tool_instance.run,
                    action_instance,
                )

                call_finished = asyncio.get_event_loop().time()
                call_duration = max(0.0, call_finished - call_started)

                normalized_result = getattr(tool_output, "result", tool_output)
                normalized_result = self._ensure_json_serializable(normalized_result)
                logger.debug(
                    f"MySQL tool {tool_name} iteration {iteration + 1} produced result: {self._format_json_snippet(normalized_result, limit=600)}"
                )
                tool_outputs.append(
                    {
                        "tool_name": tool_name,
                        "args": tool_args,
                        "result": normalized_result,
                    }
                )
                if isinstance(tool_args, dict) and "query" in tool_args:
                    tool_queries.append(tool_args["query"])

                await self._emit_stream_message(
                    stream_callback,
                    "{0} completed.\n".format(tool_name),
                    workflow_state,
                    label="workflow",
                    source="mysql_analyzer",
                )
                await self._emit_stream_message(
                    stream_callback,
                    f"{self._format_json_snippet(normalized_result)}\n\n",
                    workflow_state,
                    label="workflow",
                    source="mysql_analyzer",
                )

                if tool_stream_callback:
                    event_content = normalized_result
                    if isinstance(normalized_result, dict):
                        event_content = [normalized_result]
                    elif not isinstance(normalized_result, list):
                        event_content = [normalized_result]

                    await tool_stream_callback({
                        "type": "tool_call_result",
                        "tool_name": tool_name,
                        "result": {
                            "content": event_content,
                            "is_error": False,
                        },
                        "timestamp": call_finished,
                        "execution_time": call_duration,
                    })
                    logger.info(f"MySQL step: Sent tool_call_result for {tool_name}")

                result_message = MCPOrchestratorInputSchema(
                    query=f"Tool executed with result: {normalized_result}"
                )
                agent.memory.add_message("system", result_message)

                mysql_output = await asyncio.to_thread(
                    safe_orchestrator_run,
                    agent,
                )
                action_instance = mysql_output.action
                reasoning = mysql_output.reasoning
                iteration += 1

                if reasoning:
                    await self._emit_stream_message(
                        stream_callback,
                        f"Updated MySQL reasoning:\n{reasoning}\n\n",
                        workflow_state,
                        label="LLM response",
                        source="mysql_analyzer",
                    )

            if isinstance(action_instance, FinalResponseSchema):
                # Check if tool_outputs is empty BEFORE sending result event
                if not tool_outputs:
                    error_msg = (
                        "MySQL analyzer produced a final response without executing any mysql_agent tool calls. "
                        "Queries must be executed to provide real inventory data."
                    )
                    # Send tool stream event for MySQL step error
                    if tool_stream_callback:
                        await tool_stream_callback({
                            "type": "tool_call_error",
                            "tool_name": "mysql_analyzer",
                            "error": error_msg,
                            "timestamp": asyncio.get_event_loop().time()
                        })
                        logger.info("MySQL step: Sent tool_call_error event for empty tool_outputs")
                    raise RuntimeError(error_msg)

                # Send tool stream event for MySQL step completion
                if tool_stream_callback:
                    await tool_stream_callback({
                        "type": "tool_call_result",
                        "tool_name": "mysql_analyzer",
                        "result": {"content": [action_instance.response_text], "is_error": False},
                        "timestamp": asyncio.get_event_loop().time()
                    })
                    logger.info("MySQL step: Sent tool_call_result event")

                return {
                    "success": True,
                    "response": action_instance.response_text,
                    "reasoning": reasoning,
                    "tool_outputs": tool_outputs,
                    "tool_queries": tool_queries,
                    "data": {
                        "tool_outputs": tool_outputs,
                        "tool_queries": tool_queries,
                    },
                }
            else:
                # Send tool stream event for MySQL step failure
                if tool_stream_callback:
                    await tool_stream_callback({
                        "type": "tool_call_error",
                        "tool_name": "mysql_analyzer",
                        "error": f"Analysis incomplete after {max_iterations} iterations",
                        "timestamp": asyncio.get_event_loop().time()
                    })
                    logger.info("MySQL step: Sent tool_call_error event")

                return {
                    "success": False,
                    "response": f"Analysis incomplete after {max_iterations} iterations",
                    "reasoning": reasoning,
                    "tool_outputs": tool_outputs,
                    "tool_queries": tool_queries,
                }

        except Exception as e:
            # Send tool stream event for MySQL step error
            if tool_stream_callback:
                await tool_stream_callback({
                    "type": "tool_call_error",
                    "tool_name": "mysql_analyzer",
                    "error": str(e),
                    "timestamp": asyncio.get_event_loop().time()
                })
                logger.info(f"MySQL step: Sent tool_call_error event for exception: {e}")

            result_payload: Dict[str, Any] = {"success": False, "error": str(e)}
            if "tool_outputs" in locals():
                result_payload["tool_outputs"] = tool_outputs
                result_payload["tool_queries"] = tool_queries
            return result_payload

    async def _execute_shortage_step(
        self,
        agent: Any,
        context: Dict[str, Any],
        timeout: int,
        stream_callback: Optional[Callable[[str], Any]] = None,
        tool_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        workflow_state: Optional[Dict[str, Any]] = None,
        early_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
    ) -> Dict[str, Any]:
        """Execute shortage analyzer step."""
        try:
            query = context.get("original_query", "")
            shared_context = context.get("shared_context", "")

            # Provide full shared context to the shortage analyzer so it can
            # extract MySQL-derived component data and requirements.
            # We include context in both 'message' and 'context' and prefer
            # it over the original query for extraction downstream.
            shortage_input = {
                "company_name": "WorkflowAnalysis",
                "financial_data": shared_context or query,
                "message": f"Analyze shortage risk. Context: {shared_context}",
                "context": shared_context,
            }

            await self._emit_stream_message(
                stream_callback,
                "Calculating shortage indices and risk levels...\n",
                workflow_state,
                label="workflow",
                source="shortage_analyzer",
            )

            timestamp = datetime.utcnow().isoformat() + "Z"
            event_base = {
                "source": "shortage_analyzer",
                "timestamp": timestamp,
            }
            if workflow_state and workflow_state.get("workflow_id"):
                event_base["workflow_id"] = workflow_state["workflow_id"]

            planned_tool_count = 1

            await self._emit_early_stream_event(
                early_stream_callback,
                {
                    **event_base,
                    "type": "vllm_response_start",
                    "phase": "reasoning",
                    "message": "Shortage analyzer reasoning phase started",
                },
                workflow_state,
            )

            # Attach streaming callback to the agent so MCP tool events surface downstream
            if tool_stream_callback and hasattr(agent, "tool_stream_callback"):
                agent.tool_stream_callback = tool_stream_callback
                logger.debug("Shortage analyzer received tool stream callback for workflow execution")
            elif not tool_stream_callback:
                logger.debug("Shortage analyzer executing without tool stream callback")

            sanitized_context = self._format_json_snippet(shortage_input.get("context") or shortage_input.get("message"))
            if sanitized_context:
                chunk_text = sanitized_context
                chunk_chars = len(chunk_text)
                await self._emit_early_stream_event(
                    early_stream_callback,
                    {
                        **event_base,
                        "type": "vllm_response_chunk",
                        "phase": "reasoning",
                        "message": {
                            "content": chunk_text,
                            "chunk_index": 1,
                            "chunk_chars": chunk_chars,
                            "cumulative_chars": chunk_chars,
                        },
                    },
                    workflow_state,
                )

            async def emit_tool_event(event: Dict[str, Any]) -> None:
                delivered = False
                if tool_stream_callback:
                    try:
                        logger.info(
                            "Emitting shortage tool event via tool callback | type=%s",
                            event.get("type"),
                        )
                        callback_result = tool_stream_callback(event)
                        if inspect.isawaitable(callback_result):
                            await callback_result
                        delivered = True
                    except Exception as stream_error:
                        logger.info(f"Failed to emit shortage tool event via tool callback: {stream_error}")

                fallback_stream = None
                if workflow_state:
                    fallback_stream = workflow_state.get("stream_callback")

                if fallback_stream and not delivered:
                    try:
                        logger.info(
                            "Forwarding shortage tool event via stream callback | type=%s",
                            event.get("type"),
                        )
                        wrapped_event = {
                            "type": "mcp_tool_stream",
                            "data": event,
                            "timestamp": datetime.utcnow().isoformat() + "Z",
                        }
                        fallback_result = fallback_stream(wrapped_event)
                        if inspect.isawaitable(fallback_result):
                            await fallback_result
                    except Exception as stream_error:
                        logger.info(f"Failed to emit shortage tool event via stream callback: {stream_error}")

            await emit_tool_event(
                {
                    "type": "tool_call_start",
                    "tool_name": "ShortageIndex",
                    "args": {"context": sanitized_context or shared_context or query},
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                }
            )

            result = await agent.enhanced_shortage_analysis(shortage_input)

            await self._emit_early_stream_event(
                early_stream_callback,
                {
                    **event_base,
                    "type": "vllm_response_complete",
                    "phase": "reasoning_complete",
                    "message": {
                        "tool_count": planned_tool_count,
                        "total_chunks": 1 if sanitized_context else 0,
                        "total_chars": len(sanitized_context) if sanitized_context else 0,
                    },
                },
                workflow_state,
            )

            await self._emit_stream_message(
                stream_callback,
                (
                    "Shortage analysis complete:\n"
                    f" - Risk level: {result.risk_level}\n"
                    f" - Shortage index: {result.shortage_index:.3f}\n\n"
                ),
                workflow_state,
                label="workflow",
                source="shortage_analyzer",
            )

            await emit_tool_event(
                {
                    "type": "tool_call_result",
                    "tool_name": "ShortageIndex",
                    "result": {
                        "content": [
                            {
                                "shortage_index": result.shortage_index,
                                "risk_level": result.risk_level,
                                "company_name": result.company_name,
                            }
                        ],
                        "is_error": False,
                    },
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                }
            )

            return {
                "success": True,
                "shortage_index": result.shortage_index,
                "risk_level": result.risk_level,
                "response": result.response,
                "company_name": result.company_name
            }
            
        except Exception as e:
            await emit_tool_event(
                {
                    "type": "tool_call_error",
                    "tool_name": "ShortageIndex",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                }
            )
            await self._emit_stream_message(
                stream_callback,
                f"Shortage analysis failed: {e}\n",
                workflow_state,
                label="workflow",
                source="shortage_analyzer",
            )

            return {"success": False, "error": str(e)}

    async def _execute_alert_step(
        self,
        agent: Any,
        context: Dict[str, Any],
        timeout: int,
        stream_callback: Optional[Callable[[str], Any]] = None,
        tool_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        workflow_state: Optional[Dict[str, Any]] = None,
        early_stream_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
    ) -> Dict[str, Any]:
        """Execute alert manager step."""
        try:
            from schemas.agent_schemas import AlertManagementInputSchema

            query = context.get("original_query", "")
            shared_context = context.get("shared_context", "")
            
            # Extract shortage data if available
            shortage_data = ""
            if "shortage_calculation_result" in context:
                shortage_result = context["shortage_calculation_result"].get("result", {})
                if "shortage_index" in shortage_result:
                    shortage_data = f"shortage_index is {shortage_result['shortage_index']:.3f}, risk_level is {shortage_result.get('risk_level', 'UNKNOWN')}"
            
            alert_input = AlertManagementInputSchema(
                company_name="WorkflowAnalysis",
                analysis_data=shared_context,
                shortage_data=shortage_data,
                alert_message=f"Workflow alert for: {query}",
                message="Process workflow results and send notifications"
            )

            await self._emit_stream_message(
                stream_callback,
                "Dispatching critical alerts to stakeholders...\n",
                workflow_state,
                label="workflow",
                source="alert_manager",
            )

            if tool_stream_callback and hasattr(agent, "tool_stream_callback"):
                agent.tool_stream_callback = tool_stream_callback

            # Send tool stream events for alert processing
            if tool_stream_callback:
                await tool_stream_callback({
                    "type": "tool_call_start",
                    "tool_name": "HttpNotification",
                    "args": {},
                    "timestamp": asyncio.get_event_loop().time()
                })
                logger.info("Alert step: Sent HttpNotification tool_call_start event")

            alert_result = await agent.process_financial_analysis(alert_input)

            # Send tool stream events for alert completion
            if tool_stream_callback:
                await tool_stream_callback({
                    "type": "tool_call_result",
                    "tool_name": "HttpNotification",
                    "result": {"content": ["sent"], "is_error": False},
                    "timestamp": asyncio.get_event_loop().time()
                })

                await tool_stream_callback({
                    "type": "tool_call_start",
                    "tool_name": "MqttNotification",
                    "args": {},
                    "timestamp": asyncio.get_event_loop().time()
                })

                await tool_stream_callback({
                    "type": "tool_call_result",
                    "tool_name": "MqttNotification",
                    "result": {"content": ["sent"], "is_error": False},
                    "timestamp": asyncio.get_event_loop().time()
                })

                await tool_stream_callback({
                    "type": "tool_call_start",
                    "tool_name": "EmailNotification",
                    "args": {},
                    "timestamp": asyncio.get_event_loop().time()
                })

                await tool_stream_callback({
                    "type": "tool_call_result",
                    "tool_name": "EmailNotification",
                    "result": {"content": ["sent"], "is_error": False},
                    "timestamp": asyncio.get_event_loop().time()
                })
                logger.info("Alert step: Sent notification tool events")

            await self._emit_stream_message(
                stream_callback,
                (
                    "Alert workflow complete:\n"
                    f" - Alerts sent: {len(alert_result.alerts_sent)}\n"
                    f" - Channels: {', '.join(alert_result.notification_results or [])}\n\n"
                ),
                workflow_state,
                label="workflow",
                source="alert_manager",
            )

            return {
                "success": True,
                "alerts_sent": alert_result.alerts_sent,
                "notification_results": alert_result.notification_results,
                "alert_summary": alert_result.alert_summary
            }
            
        except Exception as e:
            # Send tool stream event for alert processing error
            if tool_stream_callback:
                await tool_stream_callback({
                    "type": "tool_call_error",
                    "tool_name": "HttpNotification",
                    "error": str(e),
                    "timestamp": asyncio.get_event_loop().time()
                })
                logger.info(f"Alert step: Sent tool_call_error event for exception: {e}")

            await self._emit_stream_message(
                stream_callback,
                f"Alert workflow failed: {e}\n",
                workflow_state,
                label="workflow",
                source="alert_manager",
            )

            return {"success": False, "error": str(e)}
    
    def _can_continue_workflow(self, pattern: WorkflowPattern, workflow_state: Dict[str, Any]) -> bool:
        """Check if workflow can continue after current step group."""
        failed_steps = workflow_state["failed_steps"]
        
        # Check if any critical (non-optional) steps failed
        for step_id in failed_steps:
            step = pattern.get_step(step_id)
            if step and not step.optional:
                return False
        
        return True
    
    def _compile_workflow_results(self, pattern: WorkflowPattern, workflow_state: Dict[str, Any]) -> Dict[str, Any]:
        """Compile final workflow results."""
        results = {
            "success": workflow_state["status"] == "completed",
            "workflow_id": workflow_state["workflow_id"],
            "pattern_id": pattern.pattern_id,
            "pattern_name": pattern.name,
            "duration": workflow_state.get("duration", 0),
            "steps_completed": len(workflow_state["completed_steps"]),
            "steps_failed": len(workflow_state["failed_steps"]),
            "step_results": workflow_state["step_results"]
        }
        
        # Add agent-specific results
        mysql_results = None
        shortage_results = None  
        alert_results = None
        
        for step_id, step_result in workflow_state["step_results"].items():
            if "mysql" in step_id:
                mysql_results = step_result.get("result")
            elif "shortage" in step_id:
                shortage_results = step_result.get("result")
            elif "alert" in step_id:
                alert_results = step_result.get("result")
        
        results.update({
            "mysql_analysis": mysql_results,
            "shortage_analysis": shortage_results,
            "alert_management": alert_results
        })
        
        return results
print(f"[debug] loading workflow_patterns from {__file__}", flush=True)
