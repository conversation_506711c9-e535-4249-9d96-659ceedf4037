execution_engine: asyncio
logger:
  transports: [console, file]
  level: info
  show_progress: true
  path: "logs/github-to-slack.jsonl"
  path_settings:
    path_pattern: "logs/github-to-slack-{unique_id}.jsonl"
    unique_id: "timestamp"
    timestamp_format: "%Y%m%d_%H%M%S"

mcp:
  servers:
    github:
      command: "docker"
      args: [
        "run",
        "-i",
        "--rm",
        "-e",
        "GITHUB_PERSONAL_ACCESS_TOKEN",
        "ghcr.io/github/github-mcp-server"
      ]
      description: "Access GitHub API operations"
    slack:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-slack"]
      description: "Access Slack API operations"
