$schema: ../../../schema/mcp-agent.config.schema.json

mcp:
  servers:
    # Slack configuration
    # Create a Slack Bot Token and get your Team ID
    # https://api.slack.com/apps
    slack:
      env:
        SLACK_BOT_TOKEN: "xoxb-your-bot-token"
        SLACK_TEAM_ID: "T01234567"

    # GitHub configuration
    # Create a GitHub Personal Access Token with repo scope
    # https://github.com/settings/tokens
    github:
      env:
        GITHUB_PERSONAL_ACCESS_TOKEN: "github_pat_your-access-token"

anthropic:
  api_key: your-anthropic-api-key