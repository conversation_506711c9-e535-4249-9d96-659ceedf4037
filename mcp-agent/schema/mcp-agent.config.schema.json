{"$defs": {"AnthropicSettings": {"additionalProperties": true, "description": "Settings for using Anthropic models in the MCP Agent application.", "properties": {"api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Api Key"}, "default_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Default Model"}, "provider": {"default": "anthropic", "title": "Bedrock", "type": "string", "enum": ["anthropic", "bedrock", "vertexai"]}, "project": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Project"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Location"}, "aws_access_key_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Access Key Id"}, "aws_secret_access_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Secret Access Key"}, "aws_session_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Session Token"}, "aws_region": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Region"}, "profile": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Profile"}}, "title": "AnthropicSettings", "type": "object"}, "AzureSettings": {"additionalProperties": true, "description": "Settings for using Azure models in the MCP Agent application.", "properties": {"api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Api Key"}, "endpoint": {"title": "Endpoint", "type": "string"}, "credential_scopes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": ["https://cognitiveservices.azure.com/.default"], "title": "Credential Scopes"}}, "required": ["endpoint"], "title": "AzureSettings", "type": "object"}, "BedrockSettings": {"additionalProperties": true, "description": "Settings for using Bedrock models in the MCP Agent application.", "properties": {"aws_access_key_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Access Key Id"}, "aws_secret_access_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Secret Access Key"}, "aws_session_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Session Token"}, "aws_region": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Aws Region"}, "profile": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Profile"}}, "title": "BedrockSettings", "type": "object"}, "VertexAISettings": {"additionalProperties": true, "description": "Settings for using VertexAI models in the MCP Agent application", "properties": {"project": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Project"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Location"}}, "title": "VertexAISettings", "type": "object"}, "CohereSettings": {"additionalProperties": true, "description": "Settings for using Cohere models in the MCP Agent application.", "properties": {"api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Api Key"}}, "title": "CohereSettings", "type": "object"}, "GoogleSettings": {"additionalProperties": true, "description": "Settings for using Google models in the MCP Agent application.", "properties": {"api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Api Key", "description": "Or use the GOOGLE_API_KEY environment variable"}, "vertexai": {"default": false, "title": "Vertexai", "type": "boolean"}, "project": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Project"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Location"}}, "title": "GoogleSettings", "type": "object"}, "LogPathSettings": {"description": "Settings for configuring log file paths with dynamic elements like timestamps or session IDs.", "properties": {"path_pattern": {"default": "logs/mcp-agent-{unique_id}.jsonl", "title": "Path Pattern", "type": "string"}, "unique_id": {"default": "timestamp", "enum": ["timestamp", "session_id"], "title": "Unique Id", "type": "string"}, "timestamp_format": {"default": "%Y%m%d_%H%M%S", "title": "Timestamp Format", "type": "string"}}, "title": "LogPathSettings", "type": "object"}, "LoggerSettings": {"description": "Logger settings for the MCP Agent application.", "properties": {"type": {"default": "console", "enum": ["none", "console", "file", "http"], "title": "Type", "type": "string"}, "transports": {"default": [], "items": {"enum": ["none", "console", "file", "http"], "type": "string"}, "title": "Transports", "type": "array", "description": "List of transports to use (can enable multiple simultaneously)"}, "level": {"default": "info", "enum": ["debug", "info", "warning", "error"], "title": "Level", "type": "string", "description": "Minimum logging level"}, "progress_display": {"default": false, "title": "Progress Display", "type": "boolean", "description": "Enable or disable the progress display"}, "path": {"default": "mcp-agent.jsonl", "title": "Path", "type": "string", "description": "Path to log file, if logger 'type' is 'file'."}, "path_settings": {"anyOf": [{"$ref": "#/$defs/LogPathSettings"}, {"type": "null"}], "default": null}, "batch_size": {"default": 100, "title": "<PERSON><PERSON> Si<PERSON>", "type": "integer", "description": "Number of events to accumulate before processing"}, "flush_interval": {"default": 2.0, "title": "Flush Interval", "type": "number", "description": "How often to flush events in seconds"}, "max_queue_size": {"default": 2048, "title": "<PERSON>", "type": "integer", "description": "Maximum queue size for event processing"}, "http_endpoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Http Endpoint", "description": "HTTP endpoint for event transport"}, "http_headers": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Http Headers", "description": "HTTP headers for event transport"}, "http_timeout": {"default": 5.0, "title": "Http Timeout", "type": "number", "description": "HTTP timeout seconds for event transport"}}, "title": "LoggerSettings", "type": "object"}, "MCPRootSettings": {"additionalProperties": true, "description": "Represents a root directory configuration for an MCP server.", "properties": {"uri": {"title": "<PERSON><PERSON>", "type": "string", "description": "The URI identifying the root. Must start with file://"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name", "description": "Optional name for the root."}, "server_uri_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Server <PERSON><PERSON>", "description": "Optional URI alias for presentation to the server"}}, "required": ["uri"], "title": "MCPRootSettings", "type": "object"}, "MCPServerAuthSettings": {"additionalProperties": true, "description": "Represents authentication configuration for a server.", "properties": {"api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Api Key"}}, "title": "MCPServerAuthSettings", "type": "object"}, "MCPServerSettings": {"additionalProperties": true, "description": "Represents the configuration for an individual server.", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name", "description": "The name of the server."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description", "description": "The description of the server."}, "transport": {"default": "stdio", "enum": ["stdio", "sse", "streamable_http", "websocket"], "title": "Transport", "type": "string", "description": "The transport mechanism."}, "command": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Command", "description": "The command to execute the server (e.g. npx) in stdio mode."}, "args": {"items": {"type": "string"}, "title": "<PERSON><PERSON><PERSON>", "type": "array", "description": "The arguments for the server command in stdio mode."}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Url", "description": "The URL for the server for SSE, Streamble HTTP or websocket transport."}, "headers": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Headers", "description": "HTTP headers for SSE or Streamable HTTP requests."}, "http_timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Http Timeout Seconds"}, "read_timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Read Timeout Seconds"}, "terminate_on_close": {"default": true, "title": "Terminate On Close", "type": "boolean"}, "auth": {"anyOf": [{"$ref": "#/$defs/MCPServerAuthSettings"}, {"type": "null"}], "default": null, "description": "The authentication configuration for the server."}, "roots": {"anyOf": [{"items": {"$ref": "#/$defs/MCPRootSettings"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Roots", "description": "Root directories this server has access to."}, "env": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Env", "description": "Environment variables to pass to the server process."}}, "title": "MCPServerSettings", "type": "object"}, "MCPSettings": {"additionalProperties": true, "description": "Configuration for all MCP servers.", "properties": {"servers": {"additionalProperties": {"$ref": "#/$defs/MCPServerSettings"}, "title": "Servers", "type": "object"}}, "title": "MCPSettings", "type": "object"}, "OpenAISettings": {"additionalProperties": true, "description": "Settings for using OpenAI models in the MCP Agent application.", "properties": {"api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Api Key"}, "reasoning_effort": {"default": "medium", "enum": ["low", "medium", "high"], "title": "Reasoning Effort", "type": "string"}, "base_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Base Url"}, "user": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "User"}, "default_headers": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "De<PERSON>ult Head<PERSON>"}, "default_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Default Model"}}, "title": "OpenAISettings", "type": "object"}, "OpenTelemetrySettings": {"description": "OTEL settings for the MCP Agent application.", "properties": {"enabled": {"default": false, "title": "Enabled", "type": "boolean"}, "exporters": {"default": [], "items": {"enum": ["console", "file", "otlp"], "type": "string"}, "title": "Exporters", "type": "array", "description": "List of exporters to use (can enable multiple simultaneously)"}, "service_name": {"default": "mcp-agent", "title": "Service Name", "type": "string"}, "service_instance_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Service Instance Id"}, "service_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Service Version"}, "sample_rate": {"default": 1.0, "title": "Sample Rate", "type": "number", "description": "Sample rate for tracing (1.0 = sample everything)"}, "otlp_settings": {"anyOf": [{"$ref": "#/$defs/TraceOTLPSettings"}, {"type": "null"}], "default": null, "description": "OTLP settings for OpenTelemetry tracing. Required if using otlp exporter."}, "path_settings": {"anyOf": [{"$ref": "#/$defs/TracePathSettings"}, {"type": "null"}], "default": null}}, "title": "OpenTelemetrySettings", "type": "object"}, "TemporalSettings": {"description": "Temporal settings for the MCP Agent application.", "properties": {"host": {"title": "Host", "type": "string"}, "namespace": {"default": "default", "title": "Namespace", "type": "string"}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Api Key"}, "tls": {"default": false, "title": "Tls", "type": "boolean"}, "task_queue": {"title": "Task Queue", "type": "string"}, "max_concurrent_activities": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "<PERSON> Concurrent Activities"}, "timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 60, "title": "Timeout Seconds"}}, "required": ["host", "task_queue"], "title": "TemporalSettings", "type": "object"}, "TraceOTLPSettings": {"description": "Settings for OTLP exporter in OpenTelemetry.", "properties": {"endpoint": {"title": "Endpoint", "type": "string", "description": "OTLP endpoint for exporting traces."}}, "required": ["endpoint"], "title": "TraceOTLPSettings", "type": "object"}, "TracePathSettings": {"description": "Settings for configuring trace file paths with dynamic elements like timestamps or session IDs.", "properties": {"path_pattern": {"default": "traces/mcp-agent-trace-{unique_id}.jsonl", "title": "Path Pattern", "type": "string"}, "unique_id": {"default": "timestamp", "enum": ["timestamp", "session_id"], "title": "Unique Id", "type": "string"}, "timestamp_format": {"default": "%Y%m%d_%H%M%S", "title": "Timestamp Format", "type": "string"}}, "title": "TracePathSettings", "type": "object"}, "UsageTelemetrySettings": {"description": "Settings for usage telemetry in the MCP Agent application.\nAnonymized usage metrics are sent to a telemetry server to help improve the product.", "properties": {"enabled": {"default": true, "title": "Enabled", "type": "boolean", "description": "Enable usage telemetry in the MCP Agent application."}, "enable_detailed_telemetry": {"default": false, "title": "Enable Detailed Telemetry", "type": "boolean", "description": "If enabled, detailed telemetry data, including prompts and agents, will be sent to the telemetry server."}}, "title": "UsageTelemetrySettings", "type": "object"}}, "additionalProperties": true, "description": "Configuration schema for MCP Agent applications", "properties": {"mcp": {"anyOf": [{"$ref": "#/$defs/MCPSettings"}, {"type": "null"}], "default": {"servers": {}}, "description": "MCP config, such as MCP servers"}, "execution_engine": {"default": "asyncio", "enum": ["asyncio", "temporal"], "title": "Execution Engine", "type": "string", "description": "Execution engine for the MCP Agent application"}, "temporal": {"anyOf": [{"$ref": "#/$defs/TemporalSettings"}, {"type": "null"}], "default": null, "description": "Settings for Temporal workflow orchestration"}, "anthropic": {"anyOf": [{"$ref": "#/$defs/AnthropicSettings"}, {"type": "null"}], "default": null, "description": "Settings for using Anthropic models in the MCP Agent application"}, "bedrock": {"anyOf": [{"$ref": "#/$defs/BedrockSettings"}, {"type": "null"}], "default": null, "description": "Settings for using Bedrock models in the MCP Agent application"}, "cohere": {"anyOf": [{"$ref": "#/$defs/CohereSettings"}, {"type": "null"}], "default": null, "description": "Settings for using Cohere models in the MCP Agent application"}, "openai": {"anyOf": [{"$ref": "#/$defs/OpenAISettings"}, {"type": "null"}], "default": null, "description": "Settings for using OpenAI models in the MCP Agent application"}, "azure": {"anyOf": [{"$ref": "#/$defs/AzureSettings"}, {"type": "null"}], "default": null, "description": "Settings for using Azure models in the MCP Agent application"}, "google": {"anyOf": [{"$ref": "#/$defs/GoogleSettings"}, {"type": "null"}], "default": null, "description": "Settings for using Google models in the MCP Agent application"}, "otel": {"anyOf": [{"$ref": "#/$defs/OpenTelemetrySettings"}, {"type": "null"}], "default": {"enabled": false, "exporters": [], "service_name": "mcp-agent", "service_instance_id": null, "service_version": null, "sample_rate": 1.0, "otlp_settings": null, "path_settings": null}, "description": "OpenTelemetry logging settings for the MCP Agent application"}, "logger": {"anyOf": [{"$ref": "#/$defs/LoggerSettings"}, {"type": "null"}], "default": {"type": "console", "transports": [], "level": "info", "progress_display": false, "path": "mcp-agent.jsonl", "path_settings": null, "batch_size": 100, "flush_interval": 2.0, "max_queue_size": 2048, "http_endpoint": null, "http_headers": null, "http_timeout": 5.0}, "description": "Logger settings for the MCP Agent application"}, "usage_telemetry": {"anyOf": [{"$ref": "#/$defs/UsageTelemetrySettings"}, {"type": "null"}], "default": {"enabled": true, "enable_detailed_telemetry": false}, "description": "Usage tracking settings for the MCP Agent application"}}, "title": "MCP Agent Configuration Schema", "type": "object", "$schema": "http://json-schema.org/draft-07/schema#"}