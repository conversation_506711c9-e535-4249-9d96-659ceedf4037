{"permissions": {"allow": ["Bash(grep:*)", "Bash(ls:*)", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__find_file", "mcp__serena__search_for_pattern", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(python:*)", "Bash(git commit:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__read_memory", "mcp__serena__list_dir", "mcp__serena__list_memories", "Bash(git add:*)", "Bash(git log:*)", "Bash(git push:*)", "mcp__serena__replace_symbol_body", "mcp__serena__find_referencing_symbols", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pwdx:*)", "Read(/merge/agent_develop/notification/**)", "Read(/merge/agent_develop/notification/tools/**)", "Bash(git merge:*)", "<PERSON><PERSON>(timeout 60 python:*)", "<PERSON><PERSON>(netstat:*)", "Bash(git pull:*)", "Bash(git checkout:*)", "Bash(git fetch:*)"], "deny": []}}