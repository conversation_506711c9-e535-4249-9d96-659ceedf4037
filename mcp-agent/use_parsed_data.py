#!/usr/bin/env python3
"""
Demonstration of using the parsed JSONL data
"""
import json
from datetime import datetime

# Load the structured data
with open('mcp_log_parsed_structured.json', 'r') as f:
    entries = json.load(f)

print(f"Total entries loaded: {len(entries):,}")
print(f"\nFirst entry:")
print(json.dumps(entries[0], indent=2))

# Example: Filter entries by level
error_entries = [e for e in entries if e['level'] == 'ERROR']
info_entries = [e for e in entries if e['level'] == 'INFO']
debug_entries = [e for e in entries if e['level'] == 'DEBUG']

print(f"\nEntries by level:")
print(f"  DEBUG: {len(debug_entries):,}")
print(f"  INFO: {len(info_entries):,}")
print(f"  ERROR: {len(error_entries):,}")

# Example: Find entries within a time range
start_time = datetime.fromisoformat("2025-07-15T11:35:00")
end_time = datetime.fromisoformat("2025-07-15T11:40:00")

time_range_entries = [
    e for e in entries 
    if start_time <= datetime.fromisoformat(e['timestamp']) <= end_time
]

print(f"\nEntries between {start_time} and {end_time}: {len(time_range_entries):,}")

# Example: Search for specific messages
search_term = "OpenAI"
matching_entries = [e for e in entries if search_term.lower() in e['message'].lower()]
print(f"\nEntries containing '{search_term}': {len(matching_entries):,}")

# Example: Group by namespace
from collections import defaultdict
namespace_groups = defaultdict(list)
for entry in entries:
    namespace_groups[entry['namespace']].append(entry)

print(f"\nNumber of unique namespaces: {len(namespace_groups)}")
print("Top 5 namespaces by entry count:")
for ns, entries_list in sorted(namespace_groups.items(), key=lambda x: len(x[1]), reverse=True)[:5]:
    print(f"  {ns}: {len(entries_list):,} entries")

# Load raw lines if needed
with open('mcp_log_parsed_raw_lines.txt', 'r') as f:
    raw_lines = f.readlines()

print(f"\nRaw lines preserved: {len(raw_lines):,}")
print(f"First raw line: {raw_lines[0].strip()}")
