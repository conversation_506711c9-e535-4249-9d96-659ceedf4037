{"permissions": {"allow": ["Bash(git add:*)", "Bash(git commit -m \"$(cat <<''EOF''\nchore: add workflow execution context files for financial analyzer\n\n🤖 Generated with [<PERSON>](https://claude.com/claude-code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\nEOF\n)\")", "Bash(git push:*)", "Bash(git remote add:*)", "Bash(git config:*)", "Bash(git remote set-url:*)", "Bash(git init:*)", "Bash(git commit:*)", "Bash(git rm:*)"], "deny": [], "ask": []}}